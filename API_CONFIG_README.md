# 库位配置API获取功能

本文档说明了新增的通过HTTP API获取库位配置的功能。

## 功能概述

系统现在支持通过HTTP API动态获取库位配置，而不再依赖静态的`storage.json`文件。这使得配置更新更加灵活，无需重新部署配置文件。

## 配置说明

### communication.json 新增字段

在 `src/cotek_laser_detect/config/communication.json` 中新增了以下配置字段：

```json
{
    "communicate": {
        // ... 现有配置 ...
        "storage_config_api": "/device/service/thirdParty/camera/config",
        "storage_config_retry_count": 10,
        "storage_config_retry_interval": 5
    }
}
```

#### 字段说明

- `storage_config_api`: 获取库位配置的API端点路径
- `storage_config_retry_count`: 获取配置失败时的重试次数
- `storage_config_retry_interval`: 重试间隔时间（秒）

## 工作流程

1. **启动时优先从API获取配置**
   - 系统启动时会首先尝试从配置的API端点获取库位配置
   - API地址: `http://{server_ip}:{server_port}{storage_config_api}`

2. **重试机制**
   - 如果API请求失败，系统会按照配置的重试次数和间隔进行重试
   - 默认重试10次，每次间隔5秒

3. **自动保存到本地文件**
   - 当成功从API获取配置后，系统会自动将配置保存到本地的 `storage.json` 文件
   - 这样可以作为备份，确保下次启动时即使API不可用也有最新的配置
   - 保存失败不会影响系统正常运行，只会记录警告日志

4. **本地文件回退**
   - 如果API获取失败（超过重试次数），系统会回退到读取本地的 `storage.json` 文件
   - 确保系统在网络问题时仍能正常启动

## API 响应格式

API应返回以下格式的JSON响应：

```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "deviceIp": "************",
            "deviceName": "lidar53",
            "port": 1,
            "enable": true,
            "region": "93",
            "stockList": [
                {
                    "nodeNo": "196794",
                    "stockName": null,
                    "detectType": 2,
                    "stockWidth": 2,
                    "nodePosition": {
                        "x": 0.0,
                        "y": 0.0
                    },
                    "stockLength": 4,
                    "stockYaw": 180,
                    "areaNo": "1112221",
                    "stockOffset": 0,
                    "pixelList": null
                }
            ]
        }
    ]
}
```

注意：`data` 字段的内容应该与现有的 `storage.json` 文件格式完全相同。

## 日志输出

系统会在日志中记录配置获取的过程：

- 当尝试从API获取配置时会记录尝试信息
- 重试时会记录重试次数和剩余次数
- 配置获取成功或失败都会有相应的日志
- 当成功从API获取配置后，会记录保存到本地文件的过程
- 如果本地文件保存失败，会记录警告信息但不影响系统运行
- 当回退到本地文件时会有警告信息

## 故障排除

### 常见问题

1. **API无响应**
   - 检查网络连接
   - 确认服务器IP和端口配置正确
   - 检查API端点路径是否正确

2. **API返回格式错误**
   - 确认API返回的JSON格式正确
   - 检查`data`字段是否存在且格式匹配

3. **本地文件也无法读取**
   - 确认`storage.json`文件存在且可读
   - 检查文件格式是否正确

### 调试建议

- 查看ROS日志中的配置加载信息
- 可以通过减少重试次数来加快测试迭代
- 测试时可以临时修改API地址来验证回退机制

## 兼容性

- 该功能完全向后兼容现有的本地文件配置方式
- 如果不配置API相关字段，系统会直接使用本地文件
- 现有的`storage.json`文件格式保持不变 
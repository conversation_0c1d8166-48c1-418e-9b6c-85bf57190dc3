# 保存检测点云服务使用说明

## 功能说明
当激光检测不准确时，可以通过ROS服务保存当前用于检测的点云数据（detection_cloud_），用于分析问题。

## 服务配置
在 `config/detect.json` 中设置 `"save": true` 来开启保存检测点云的服务：

```json
{
  "detect": [
    {
      "frame": "lidar53",
      "save": true,        // 开启保存服务
      "calibration": false,
      "origin_filter_roi": {
        // ... ROI配置
      }
    }
  ]
}
```

## 使用方法

### 1. 基本使用（自动生成时间戳文件名）
```bash
rosservice call /save_detection_cloud_lidar53 "{filename: ''}"
```

### 2. 指定文件名
```bash
rosservice call /save_detection_cloud_lidar53 "{filename: 'problem_case_1'}"
```

## 服务响应

服务会返回以下信息：
- `success`: 是否保存成功
- `message`: 操作结果消息
- `saved_path`: 保存的完整路径

成功响应示例：
```
success: True
message: "Detection cloud saved successfully"
saved_path: "/path/to/package/config/detection_clouds/lidar53_detection_20231201_143022_123.pcd"
```

## 文件保存位置

点云文件保存在：`config/detection_clouds/` 目录下

### 文件命名规则：
- 自动命名：`{frame}_detection_{timestamp}.pcd`
- 手动命名：`{filename}.pcd`

## 注意事项

1. 只有配置了 `"save": true` 的激光设备才会开启此服务
2. 服务只保存当前的 `detection_cloud_`（经过处理后用于检测的点云）
3. 如果当前没有有效的检测点云，服务会返回失败
4. 文件以二进制PCD格式保存，可用PCL工具或CloudCompare等软件查看

## 使用场景

- 检测结果异常时，保存当时的点云进行离线分析
- 调试检测算法时，收集不同场景的点云数据
- 验证ROI过滤和坐标变换是否正确 
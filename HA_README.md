# 双机热备功能使用说明

## 概述

本系统已实现双机热备功能，支持keepalived方式的主备切换。在HA模式下，主机和备机都会启动程序，但只有主机（拥有VIP的机器）会执行检测和上报任务，备机处于待命状态。当主机故障时，keepalived会将VIP切换到备机，备机会自动接管所有功能。

## 功能特点

- **无缝切换**: 主备机均运行程序，切换时无需重启进程
- **智能检测**: 自动检测当前机器是否拥有VIP
- **状态监控**: 实时监控HA状态变化
- **完全待命**: 备机完全不执行业务逻辑，只等待主机故障切换
- **配置灵活**: 支持指定网络接口或检查所有接口

## 配置方法

### 1. 修改通信配置文件

编辑 `src/cotek_laser_detect/config/communication.json`，添加HA配置：

```json
{
    "communicate": {
        "ha": {
            "enable": true,
            "vip_address": "*************",
            "interface": "eth0",
            "check_interval": 5.0
        }
    }
}
```

#### 配置参数说明

- `enable`: 是否启用HA模式（必填）
- `vip_address`: keepalived配置的虚拟IP地址（必填）
- `interface`: 网络接口名称，可选。留空则检查所有接口
- `check_interval`: HA状态检查间隔，单位秒（默认5秒）

## 工作原理

### 1. VIP检测机制

程序通过检查网络接口来确定当前机器是否拥有VIP：
- 使用系统API `getifaddrs()` 获取网络接口信息
- 检查指定的VIP是否绑定到当前机器的网络接口
- 支持指定接口检查或全接口检查

### 2. 状态控制

- **Master模式**: 执行所有功能（检测、上报、状态监控）
- **Backup模式**: 完全待命状态，不执行任何检测和上报功能
  - 不处理点云数据
  - 不执行库位检测
  - 不上报库位状态
  - 不上报激光器存活状态（备机激光器始终为不存活状态）
- **状态切换**: 当VIP切换时，自动更新所有检测器的工作模式

## 使用步骤

### 1. 部署准备
1. 在两台机器上部署相同的程序代码
2. 配置keepalived服务
3. 修改communication.json启用HA模式

### 2. 启动服务
```bash
# 在主机和备机上都执行
sudo systemctl start keepalived
roslaunch cotek_laser_detect laser_detect.launch
```

### 3. 验证状态
检查程序日志，应该看到类似输出：
```
[INFO] HA Mode enabled. Current status: master (VIP: *************)
[INFO] livox_front HA mode enabled, role: master
```

### 4. 测试切换
停止主机的keepalived服务，观察备机是否自动接管。

## 备机运行状态详解

### 备机在待命状态下的行为：

1. **程序启动**: 备机正常启动所有模块和线程
2. **VIP检测**: 检测到当前机器没有VIP，进入备机模式
3. **完全待命**: 
   - 检测线程处于空转状态，不处理任何点云数据
   - 不执行库位检测算法
   - 激光器IsAlive()方法始终返回false
   - 不上报任何状态信息
4. **状态监控**: 只有HA状态检查定时器在工作，每5秒检查一次VIP状态
5. **切换响应**: 一旦检测到VIP切换到本机，立即激活所有功能

### 备机的日志输出：
```
[INFO] HA Mode enabled. Current status: backup (VIP: *************)
[INFO] livox_front HA mode enabled, role: backup
[DEBUG] livox_front backup mode: standby
```

### 主备切换时的日志：
```
[WARN] HA Status changed: backup -> master
[INFO] livox_front HA role changed to: master
```

## 注意事项

1. **配置一致性**: 确保两台机器的程序配置完全一致
2. **时间同步**: 建议使用NTP保持两台机器时间同步
3. **网络稳定**: 确保两台机器之间网络通信稳定
4. **权限设置**: 程序需要有读取网络接口信息的权限
5. **测试验证**: 部署后务必进行完整的切换测试

通过以上配置，您就可以实现完整的双机热备功能，确保系统的高可用性。 
/**
 * Copyright (c) 2022 CoTEK Inc. All rights reserved.
 */
#ifndef INCLUDE_LASER_H_
#define INCLUDE_LASER_H_

#include <iostream>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>

#include <sensor_msgs/PointCloud2.h>

#include "cotek_enum.h"

namespace cotek_laser_detect {

class Laser {
 public:
  Laser() = delete;
  explicit Laser(const LaserStorageOption& option)
      : option_(option) {}
  ~Laser() {}

  bool Init();
  VaildPclPointCloud GetPointCloud(const std::string& frame);

 private:
  void MultiLaserCallBack(const sensor_msgs::PointCloud2::ConstPtr &msg);

  LaserStorageOption option_;

  std::vector<ros::Subscriber> subscribers_;

  std::map<std::string, VaildPclPointCloud> pc_;

  std::mutex mutex_;

};

}  // namespace cotek_cam_detect

#endif  // INCLUDE_CAMERA_DETECT_H_

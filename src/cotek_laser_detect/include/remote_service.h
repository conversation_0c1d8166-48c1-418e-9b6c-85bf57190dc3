/**
 * Copyright (c) 2020 COTEK Inc. All rights reserved.
 */
#ifndef INCLUDE_REMOTE_SERVICE_H_
#define INCLUDE_REMOTE_SERVICE_H_
#include <string>
#include <tuple>
#include <sstream>
#include <ros/ros.h>
#include <json/json.h>
#include "httplib.h"
#include <mutex>

namespace util {
class RemoteServiceEntry final {
 public:
  RemoteServiceEntry() = delete;
  RemoteServiceEntry(const std::string &ip, int port, const std::string &api)
      : entry_(ip, port, api) {}

  inline const char *Ip() const { return std::get<0>(entry_).c_str(); }
  inline int Port() const { return std::get<1>(entry_); }
  inline const char *Api() const { return std::get<2>(entry_).c_str(); }

 private:
  std::tuple<std::string, int, std::string> entry_;
};

class RemoteService final {
 public:
  static std::string GetString(const RemoteServiceEntry &entry, int timeout_seconds = 3) {
    httplib::Client client(entry.Ip(), entry.Port());
    
    // 设置超时
    client.set_timeout_sec(timeout_seconds);
    client.set_read_timeout(timeout_seconds, 0);
    
    const auto &res = client.Get(entry.Api());
    if (res) {
      if (res->status != 200) {
        ROS_ERROR("http error, status: %d", res->status);
      }
      return res->body;
    } else {
      ROS_ERROR("HTTP request timeout or failed");
      return std::string();
    }
  }

  static bool is_request_success(const std::string& response_body) {
    // Using a mutex to protect JSON parsing operations
    static std::mutex request_mutex;
    std::lock_guard<std::mutex> lock(request_mutex);
    
    Json::Value root;
    Json::CharReaderBuilder builder;
    builder["collectComments"] = false;
    std::string errs;
    std::istringstream iss(response_body);
    
    // 解析 JSON
    if (!Json::parseFromStream(builder, iss, &root, &errs)) {
        ROS_ERROR_STREAM("post failed (invalid JSON) !!! Body: " << response_body);
        ROS_ERROR_STREAM("JSON parse error: " << errs);
        return false;
    }
    // 检查关键字段是否存在
    if (root.isObject() && root.isMember("message")) {
        std::string message = root["message"].asString();
        
        // 转换为小写进行比较
        std::transform(message.begin(), message.end(), message.begin(), ::tolower);

        // 检查message是否为success
        if (message == "success") {
            return true;
        }
    }

    // 其他情况均视为失败
    ROS_ERROR_STREAM("post failed !!! Body: " << response_body);
    return false;
  }

  static bool PostString(const RemoteServiceEntry &entry,
                         const std::string &json_str,
                         int timeout_seconds = 10) {
    httplib::Client client(entry.Ip(), entry.Port());
    
    // 设置超时
    client.set_timeout_sec(timeout_seconds);
    client.set_read_timeout(timeout_seconds, 0);
    
    ROS_INFO_STREAM("Sending request to: " << entry.Ip() << ":" << entry.Port() << entry.Api());
    // ROS_INFO_STREAM("Request body: " << json_str);
    const auto &res = client.Post(entry.Api(), json_str, "application/json");
    if (res == nullptr) {
      ROS_ERROR("response empty!");
      return false;
    }

    if (res->status != 200) {
      ROS_ERROR_STREAM("Response status: " << res->status);
      ROS_ERROR_STREAM("Response body: " << res->body);
      return false;
    }else{
      ROS_INFO_STREAM("Response status: " << res->status);
      ROS_INFO_STREAM("Response body: " << res->body);
    }

    if (is_request_success(res->body)) {
      return true;
    } else {
      return false;
    }
  }


};
}  // namespace util
#endif  // COTEK_COMMON_INCLUDE_COTEK_COMMON_REMOTE_SERVICE_H_

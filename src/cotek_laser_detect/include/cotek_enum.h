/**
 * Copyright (c) 201906 CoTEK Inc. All rights reserved.
 */
#ifndef INCLUDE_COTEK_ENUM_H_
#define INCLUDE_COTEK_ENUM_H_

#include <ros/ros.h>

#include <iostream>
#include <map>
#include <memory>
#include <string>
#include <vector>
#include <set>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

namespace cotek_laser_detect {

enum class StockState : uint8_t {
  NONE = 0,    // 未知
  OCCUPY = 1,  // 占据 (满笼)
  FREE = 2,    // 空闲
  HALF_FULL = 3 // 半笼
};

enum class SaveMode : uint8_t {
  NONE = 0,         // 不保存
  ALL = 1,          // 全保存
  SAVE_FREE = 2,    // 保存空库位
  SAVE_OCCUPY = 3,  // 保存满库位
};

struct CommunicateOption {
  std::string server_ip;
  int server_port;
  std::string local_ip;
  int local_port;
  std::string server_api;
  std::string local_api;
  double update_duration;
  double accumulate_duration;
  double duration;
  std::string laser_alive_api;
  double laser_alive_check_interval;
  std::string storage_config_api;
  std::string storage_config_server_ip;  // 专门用于拉取库位配置的服务器IP
  int storage_config_server_port; // 专门用于拉取库位配置的服务器端口
  int storage_config_retry_count;
  double storage_config_retry_interval;
  
  // HA (High Availability) 相关配置
  bool enable_ha = false;                // 是否启用HA模式
  std::string vip_address;               // 虚拟IP地址
  std::string ha_interface;              // HA网络接口名称(可选)
  double ha_check_interval = 5.0;        // HA状态检查间隔(秒)
};

struct VaildPclPointCloud {
  bool vaild = false;
  pcl::PointCloud<pcl::PointXYZ> cloud;
};

struct Point {
  double x;
  double y;
};

struct Tf {
  double x;
  double y;
  double z;
  double roll;
  double pitch;
  double yaw;
};

struct StockOption {
  int id;
  std::string stock_id;
  std::string area_id;
  std::string laser_frame;
  std::vector<Point> stock_points;
  bool detect_fill_level = false; // 是否检测满笼/半笼
  Point stock_center;  // 库位中心点坐标
  double stock_width;  // 库位宽度
  double stock_length; // 库位长度
  double stock_yaw;    // 库位偏移角度
  double stock_offset; // 库位偏移量
};

struct RoiArea {
  double x_min;
  double x_max;
  double y_min;
  double y_max;
  double z_min;
  double z_max;
};

struct DetectOption
{
  bool save;
  bool calibration;
  RoiArea origin_roi_filter;
  // 满笼和半笼的高度阈值
  double full_height_threshold = 1.0;  // 满笼高度阈值（米）
  double half_height_threshold = 0.4;  // 半笼高度阈值（米）
  // Additional fields for radar reporting
  std::string ip_address;    // Radar device IP address
  std::string region;        // Region identifier
  
  // 调试功能字段
  bool save_empty_stock_cloud = false;      // 是否保存空库位的点云
  bool save_problem_stock_cloud = false;    // 是否保存有问题的点云（如高度异常）
  std::set<std::string> debug_stock_ids;    // 强制保存点云的库位ID列表
};

struct LaserStorageOption {
  CommunicateOption communicate_option;
  std::map<std::string, DetectOption> detect_option;
  std::map<std::string, Tf> tf_option;
  std::map<std::string, std::vector<StockOption>> stock_option;
  std::string package_path;
  // 所有 area 下的 stock_option 的 id 集合
  std::map<std::string, std::vector<std::string>> area_stock_id_map;
  
  // 全局点云数量阈值配置
  int point_cloud_threshold = 20;  // 默认点云数量阈值
};

struct CalibrationParams {
  double roll = 0.0;
  double pitch = 0.0;
  double yaw = 0.0;
  double tx = 0.0;
  double ty = 0.0;
  double tz = 0.0;
};

struct CorrespondencePoint {
  pcl::PointXYZ lidar_point;
  pcl::PointXYZ world_point;
};

}  // namespace cotek_cam_detect

#endif  // INCLUDE_COTEK_ENUM_H_

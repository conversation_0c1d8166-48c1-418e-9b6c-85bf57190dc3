#ifndef INCLUDE_CALIBRATION_H_
#define INCLUDE_CALIBRATION_H_

#include <ros/ros.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/segmentation/sac_segmentation.h>
#include <pcl/common/transforms.h>
#include <pcl/segmentation/region_growing.h>
#include <pcl/features/normal_3d.h>
#include <pcl/filters/extract_indices.h>
#include <Eigen/Core>
#include <Eigen/Geometry>
#include <iostream>
#include <string>
#include <vector>
#include <fstream>
#include <yaml-cpp/yaml.h>
#include "cotek_enum.h"
#include <ros/package.h>

namespace cotek_laser_detect {

class Calibration {
public:
    Calibration(const std::string& frame_name = "");
    
    // Core functionality
    bool calibrate(pcl::PointCloud<pcl::PointXYZ>::Ptr cloud, 
                   const std::vector<CorrespondencePoint>& correspondence_points,
                   bool save_debug_files = false);
    
    // Load/save functionality
    bool loadCorrespondencePointsFromYaml(const std::string& yaml_file, const std::string& frame_name,
                                         std::vector<CorrespondencePoint>& correspondence_points);
    
    // Getter methods to access calibration parameters
    CalibrationParams getCalibrationParams() const;
    
    // Apply calibration to a point cloud
    void transformPointCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& input_cloud,
                            pcl::PointCloud<pcl::PointXYZ>::Ptr& output_cloud);

    // 本地调试路径
    std::string package_path_ = ros::package::getPath("cotek_laser_detect");
    
private:
    // Define residual calculation structures
    struct GroundPlaneResidual {
        pcl::PointXYZ lidar_pt;

        GroundPlaneResidual(pcl::PointXYZ pt) : lidar_pt(pt) {}

        double computeResidual(const double* params) const {
            const double roll = params[0];
            const double pitch = params[1];
            const double yaw = params[2];
            const double tx = params[3];
            const double ty = params[4];
            const double tz = params[5];

            // Calculate rotation matrix (ZYX order)
            Eigen::Matrix3d R;
            R = Eigen::AngleAxisd(yaw, Eigen::Vector3d::UnitZ()) *
                Eigen::AngleAxisd(pitch, Eigen::Vector3d::UnitY()) *
                Eigen::AngleAxisd(roll, Eigen::Vector3d::UnitX());

            // Point transformation
            Eigen::Vector3d lidar_p(lidar_pt.x, lidar_pt.y, lidar_pt.z);
            Eigen::Vector3d world_p = R * lidar_p + Eigen::Vector3d(tx, ty, tz);
            
            return world_p.z();
        }

        Eigen::VectorXd computeNumericalJacobian(const double* params, double epsilon = 1e-6) const {
            Eigen::VectorXd jacobian(6);
            double base_residual = computeResidual(params);

            for (int i = 0; i < 6; ++i) {
                double perturbed_params[6];
                std::copy(params, params + 6, perturbed_params);
                perturbed_params[i] += epsilon;
                double perturbed_residual = computeResidual(perturbed_params);
                jacobian[i] = (perturbed_residual - base_residual) / epsilon;
            }
            return jacobian;
        }
    };

    struct CorrespondenceResidual {
        pcl::PointXYZ lidar_pt;
        pcl::PointXYZ world_pt;

        CorrespondenceResidual(pcl::PointXYZ l, pcl::PointXYZ w) 
            : lidar_pt(l), world_pt(w) {}

        Eigen::Vector3d computeResidual(const double* params) const {
            const double roll = params[0];
            const double pitch = params[1];
            const double yaw = params[2];
            const double tx = params[3];
            const double ty = params[4];
            const double tz = params[5];

            // Calculate rotation matrix (ZYX order)
            Eigen::Matrix3d R;
            R = Eigen::AngleAxisd(yaw, Eigen::Vector3d::UnitZ()) *
                Eigen::AngleAxisd(pitch, Eigen::Vector3d::UnitY()) *
                Eigen::AngleAxisd(roll, Eigen::Vector3d::UnitX());

            // Point transformation
            Eigen::Vector3d lidar_p(lidar_pt.x, lidar_pt.y, lidar_pt.z);
            Eigen::Vector3d world_p = R * lidar_p + Eigen::Vector3d(tx, ty, tz);
            
            return world_p - Eigen::Vector3d(world_pt.x, world_pt.y, world_pt.z);
        }

        Eigen::Matrix<double, 3, 6> computeNumericalJacobian(const double* params, double epsilon = 1e-6) const {
            Eigen::Matrix<double, 3, 6> jacobian;
            const Eigen::Vector3d base_residual = computeResidual(params);

            for (int i = 0; i < 6; ++i) {
                double perturbed_params[6];
                std::copy(params, params + 6, perturbed_params);
                perturbed_params[i] += epsilon;
                
                const Eigen::Vector3d perturbed_residual = computeResidual(perturbed_params);
                jacobian.col(i) = (perturbed_residual - base_residual) / epsilon;
            }
            return jacobian;
        }
    };

    bool fitPlaneWithRegionGrowing(pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud,
                                   pcl::PointIndices::Ptr& inliers, 
                                   pcl::ModelCoefficients::Ptr& coefficients);

    CalibrationParams calib_params_;
    std::string frame_name_;
};

} // namespace cotek_laser_detect

#endif // INCLUDE_CALIBRATION_H_ 
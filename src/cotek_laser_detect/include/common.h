#ifndef INCLUDE_COMMON_H_
#define INCLUDE_COMMON_H_

#include <cmath>

namespace common {

#define PI 3.141593f

template <typename T>
T Deg2Rad(const T& deg) {
  return deg / 180.0 * M_PI;
}

template <typename T>
T Rad2Deg(const T& rad) {
  return rad / M_PI * 180.0;
}

float normalize_angle(float angle) {
  float a = fmod(fmod(angle, 2.0f * PI) + 2.0f * PI, 2.0f * PI);
  if (a > PI) a -= 2.0f * PI;
  return a;
}

}

#endif
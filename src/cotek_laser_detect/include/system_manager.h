#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

#include <ros/ros.h>
#include <thread>
#include <mutex>
#include <atomic>
#include <memory>
#include <map>
#include <string>

#include "config.h"
#include "laser_detect.h"
#include "status_reporter.h"
#include "ha_manager.h"

// 系统管理类，负责管理所有对象的生命周期
class SystemManager {
public:
  SystemManager();
  ~SystemManager();
  
  bool Initialize(const std::string& package_path);
  bool Restart();
  void Shutdown();
  bool IsInitialized() const { return is_initialized_; }
  
  // 公共访问方法
  const std::map<std::string, std::shared_ptr<cotek_laser_detect::LaserDetector>>& GetDetectors() const { return detect_ptr_map_; }
  cotek_laser_detect::StatusReporter* GetStatusReporter() const { return status_reporter_.get(); }
  
private:
  bool LoadAllConfigs();
  bool InitializeDetectors();
  bool InitializeStatusReporter();
  bool InitializeHAManager();
  bool InitializeTimers();
  void CleanupAll();
  
  // 配置文件路径
  std::string package_path_;
  std::string communication_path_;
  std::string storage_path_;
  std::string tf_path_;
  std::string detect_path_;
  
  // 配置选项
  LaserStorageOption option_;
  
  // 主要对象
  std::map<std::string, std::shared_ptr<cotek_laser_detect::LaserDetector>> detect_ptr_map_;
  std::unique_ptr<cotek_laser_detect::StatusReporter> status_reporter_;
  std::unique_ptr<cotek_laser_detect::HAManager> ha_manager_;
  
  // ROS相关
  std::unique_ptr<ros::NodeHandle> nh_;
  ros::Timer stock_timer_;
  ros::Timer laser_alive_timer_;
  ros::Timer ha_status_timer_;
  
  // 状态管理
  std::atomic<bool> is_initialized_;
  std::atomic<bool> is_shutting_down_;
  mutable std::mutex mutex_;
  
  // HA状态
  bool ha_enabled_;
  bool last_master_status_;
};

#endif // SYSTEM_MANAGER_H 
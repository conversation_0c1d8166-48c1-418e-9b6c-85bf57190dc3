#ifndef HA_MANAGER_H
#define HA_MANAGER_H

#include <string>
#include <ros/ros.h>

namespace cotek_laser_detect {

class HAManager {
public:
    HAManager(const std::string& vip_address = "", const std::string& interface = "");
    ~<PERSON><PERSON>anager();
    
    /**
     * @brief Check if current machine is the master (has VIP)
     * @return true if this machine is master, false if backup
     */
    bool IsMaster() const;
    
    /**
     * @brief Set the VIP address to check
     * @param vip_address The virtual IP address
     */
    void SetVIPAddress(const std::string& vip_address);
    
    /**
     * @brief Set the network interface to check
     * @param interface The network interface name (e.g., "eth0")
     */
    void SetInterface(const std::string& interface);
    
    /**
     * @brief Get current master status as string
     * @return "master" or "backup"
     */
    std::string GetStatusString() const;

private:
    /**
     * @brief Check if VIP is assigned to any network interface
     * @return true if VIP is found
     */
    bool CheckVIPAssigned() const;
    
    /**
     * @brief Check if VIP is assigned to specific interface
     * @param interface_name The interface to check
     * @return true if VIP is found on the interface
     */
    bool CheckVIPOnInterface(const std::string& interface_name) const;
    
    std::string vip_address_;
    std::string interface_;
};

} // namespace cotek_laser_detect

#endif // HA_MANAGER_H 
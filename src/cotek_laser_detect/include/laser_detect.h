/**
 * Copyright (c) 2022 CoTEK Inc. All rights reserved.
 */
#ifndef INCLUDE_LASER_DETECT_H_
#define INCLUDE_LASER_DETECT_H_

#include <iostream>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>
#include <queue>
#include <atomic>

#include <sensor_msgs/PointCloud2.h>
#include "cotek_laser_detect/calibrate.h"
#include "cotek_laser_detect/save_detection_cloud.h"
#include "remote_service.h"
#include "thread_pool.h"
#include "stock.h"
#include "cotek_enum.h"
#include <json/json.h>

namespace cotek_laser_detect
{

  class LaserDetector
  {
  public:
    LaserDetector() = delete;
    explicit LaserDetector(const LaserStorageOption &option,
                           const std::string &frame)
        : option_(option), frame_(frame), thread_pool_(8), ha_mode_enabled_(false), is_master_(true) {}

    ~LaserDetector()
    {
      // Signal threads to shut down
      shutdown_flag_ = true;
      
      if (executor_)
      {
        executor_->join();
        executor_ = nullptr;
      }
      if (accumulator_)
      {
        accumulator_->join();
        accumulator_ = nullptr;
      }
      
      // Clear any resources
      std::lock_guard<std::mutex> lock(accumulated_cloud_mutex_);
      while (!cloud_queue_.empty()) {
        cloud_queue_.pop();
      }
      accumulated_cloud_.reset();
    }

    // bool Init() { return true; };
    bool Init();
    void Run();
    void CycleSendStorageInfo();
    void SendStorageInfo();
    std::string SendStorageInfo(const std::string &area_id);
    void UpdateStocks(std::map<std::string, StockState> detectResult);
    std::map<std::string, std::shared_ptr<Stock>> GetCurrentStatus() const
    {
      std::lock_guard<std::mutex> lock(mutex_);
      return id_stocks_;
    }

    // Add method to check lidar existence status
    bool IsAlive() const;
    
    // Get the detector configuration options
    const LaserStorageOption& GetOption() const {
      return option_;
    }
    
    // HA (High Availability) related methods
    void SetHAMode(bool enabled, bool is_master = true) {
      ha_mode_enabled_ = enabled;
      is_master_ = is_master;
      if (ha_mode_enabled_) {
        ROS_INFO_STREAM(frame_ << " HA mode " << (enabled ? "enabled" : "disabled") 
                        << ", role: " << (is_master ? "master" : "backup"));
      }
    }
    
    void UpdateHAStatus(bool is_master) {
      if (ha_mode_enabled_ && is_master_ != is_master) {
        is_master_ = is_master;
        ROS_INFO_STREAM(frame_ << " HA role changed to: " << (is_master ? "master" : "backup"));
      }
    }
    
    bool IsHAEnabled() const { return ha_mode_enabled_; }
    bool IsMaster() const { return is_master_; }

  private:
    void LaserCallBack(const sensor_msgs::PointCloud2::ConstPtr &msg);
    void Runner();
    void AccumulateProcess();
    void CalLivoxInstallParams();

    bool CalibrateRequest(cotek_laser_detect::calibrate::Request &req,
                          cotek_laser_detect::calibrate::Response &res);
    bool SaveDetectionCloudRequest(cotek_laser_detect::save_detection_cloud::Request &req,
                                   cotek_laser_detect::save_detection_cloud::Response &res);
    bool Process();
    std::map<std::string, StockState> Detect();
    void updateStockHeightScore(const std::string& stock_id, float height_score);

    std::string PackStorageInfo();
    std::vector<StockOption> stock_option_;
    LaserStorageOption option_;
    DetectOption detect_option_;
    Tf tf_;
    std::string tf_path_;

    ros::Publisher cloud_pub_;
    std::vector<ros::Subscriber> sub_;
    ros::ServiceServer calibrate_srv_;
    ros::ServiceServer save_detection_cloud_srv_;
    ros::Time save_time_;

    // Add timestamp for last received data
    ros::Time last_data_received_;

    mutable std::mutex mutex_;
    std::shared_ptr<std::thread> executor_;
    std::shared_ptr<std::thread> accumulator_;

    std::string frame_;
    std::string saved_pcd_path_;

    VaildPclPointCloud vaild_cloud_;

    std::queue<pcl::PointCloud<pcl::PointXYZ>::Ptr> cloud_queue_;
    pcl::PointCloud<pcl::PointXYZ>::Ptr accumulated_cloud_;
    pcl::PointCloud<pcl::PointXYZ>::Ptr detection_cloud_; // Point cloud used for detection
    std::mutex accumulated_cloud_mutex_;
    bool accumulated_cloud_updated_;
    std::atomic<bool> shutdown_flag_{false}; // Flag to signal threads to shut down

    uint8_t get_frame_num_;
    pcl::PointCloud<pcl::PointXYZ> all_cloud_;

    bool request_calibrate_;

    bool MakeValidStockMap();

    std::mutex stock_mutex_;

    // 以唯一库位编号存放库位
    std::map<std::string, std::shared_ptr<Stock>> id_stocks_;
    // 存放对应库区的各个库位
    std::map<std::string, std::vector<std::string>> area_stocks_;

    ThreadPool thread_pool_;

    // 激光标定参数
    CalibrationParams calib_params_;
    std::vector<CorrespondencePoint> correspondence_points_;

    void AddCorrespondencePoint(const pcl::PointXYZ &lidar_p, const pcl::PointXYZ &world_p)
    {
      correspondence_points_.push_back({lidar_p, world_p});
    }

    // 添加库位坐标缓存映射
    std::map<std::string, std::pair<Point, Point>> stock_boundaries_cache_;  // stock_id -> (min_corner, max_corner)
    std::map<std::string, std::pair<double, double>> stock_z_ranges_cache_;  // stock_id -> (z_min, z_max)
    
    // 预计算库位边界坐标的方法
    void PrecomputeStockBoundaries();
    
    // HA (High Availability) related members
    bool ha_mode_enabled_;
    bool is_master_;
  };

} // namespace cotek_cam_detect

#endif // INCLUDE_CAMERA_DETECT_H_

#ifndef STATUS_REPORTER_H
#define STATUS_REPORTER_H

#include <map>
#include <memory>
#include <string>
#include <vector>
#include <ros/ros.h>
#include "laser_detect.h"
#include "json/json.h"

namespace cotek_laser_detect {
const int MAX_PENDING_TASKS = 50;

class StatusReporter {
public:
  StatusReporter(LaserStorageOption option);
  ~StatusReporter();

  void SendStorageInfo(const std::string& storage_info_string);
  void SendLaserAliveInfo(const std::string& laser_status_string);
  void reportAllStockStatus(
      const std::map<std::string, std::shared_ptr<cotek_laser_detect::LaserDetector>> &detectors);
  void reportLaserAliveStatus(
      const std::map<std::string, std::shared_ptr<cotek_laser_detect::LaserDetector>> &detectors);

  std::string PackStorageInfo(
      std::map<std::string, std::shared_ptr<cotek_laser_detect::Stock>> id_stocks);

private:
  template <typename T>
  std::string GetCurrentTime();
  
  std::string GetFormattedDateTime();

  LaserStorageOption option_;
  ThreadPool thread_pool_;
  std::atomic<int> taskcnt_{0};
};

} // namespace cotek_laser_detect

#endif // STATUS_REPORTER_H 
/**
 * Copyright (c) 2022 CoTEK Inc. All rights reserved.
 */
#ifndef INCLUDE_CONFIG_H_
#define INCLUDE_CONFIG_H_

#include <fstream>
#include <json/json.h>
#include "cotek_enum.h"
#include "remote_service.h"
#include <ros/ros.h>
#include <ctime>
#include <thread>
#include <chrono>
#include <sys/stat.h>
#include <string>

using namespace cotek_laser_detect;

// Helper function to create directory if it doesn't exist
static bool CreateDirectoryIfNotExists(const std::string& dir_path) {
  struct stat st;
  if (stat(dir_path.c_str(), &st) == 0) {
    if (S_ISDIR(st.st_mode)) {
      return true; // Directory already exists
    } else {
      ROS_ERROR_STREAM("Path exists but is not a directory: " << dir_path);
      return false;
    }
  } else {
    // Directory doesn't exist, try to create it
    if (mkdir(dir_path.c_str(), 0755) == 0) {
      ROS_INFO_STREAM("Created backup directory: " << dir_path);
      return true;
    } else {
      ROS_ERROR_STREAM("Failed to create backup directory: " << dir_path);
      return false;
    }
  }
}

// Helper function to get backup file path
static std::string GetBackupFilePath(const std::string& original_path, const std::string& timestamp) {
  // Find the last slash to separate directory and filename
  size_t last_slash = original_path.find_last_of('/');
  std::string dir_path = (last_slash != std::string::npos) ? 
                        original_path.substr(0, last_slash) : ".";
  std::string filename = (last_slash != std::string::npos) ? 
                        original_path.substr(last_slash + 1) : original_path;
  
  // Create backup directory path
  std::string backup_dir = dir_path + "/backups";
  
  // Create backup directory if it doesn't exist
  if (!CreateDirectoryIfNotExists(backup_dir)) {
    // Fallback to original directory if backup directory creation fails
    ROS_WARN_STREAM("Failed to create backup directory, using original directory");
    return original_path + ".bak_" + timestamp;
  }
  
  // Create backup file path
  return backup_dir + "/" + filename + ".bak_" + timestamp;
}

static std::string GetStringFromFile(const std::string &file_path) {
  std::ifstream ifs(file_path);
  if (!ifs.is_open()) {
    ifs.close();
    return std::string();
  }

  std::stringstream ss;
  ss << ifs.rdbuf();
  ifs.close();
  return ss.str();
}

static bool SaveStringToFile(const std::string &file_path, const std::string &content) {
  try {
    std::ofstream ofs(file_path);
    if (!ofs.is_open()) {
      ROS_ERROR_STREAM("Failed to open file for writing: " << file_path);
      return false;
    }
    
    ofs << content;
    ofs.close();
    
    if (ofs.good()) {
      ROS_INFO_STREAM("Successfully saved content to file: " << file_path);
      return true;
    } else {
      ROS_ERROR_STREAM("Error occurred while writing to file: " << file_path);
      return false;
    }
  } catch (const std::exception& e) {
    ROS_ERROR_STREAM("Exception while saving to file " << file_path << ": " << e.what());
    return false;
  }
}

static bool LoadDetectConfig(const std::string& path, LaserStorageOption* option) {
  std::string json_str = GetStringFromFile(path);
  if (json_str.empty()) {
    ROS_ERROR_STREAM(path << " is empty.");
    return false;
  }

  Json::Value root;
  Json::CharReaderBuilder builder;
  std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
  std::string errors;
  if (!reader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errors)) {
    ROS_ERROR("Detect config json file data format is error.");
    ROS_ERROR_STREAM("config error info: " << errors);
    return false;
  }

  try {
    ROS_INFO_STREAM("\n");
    ROS_INFO_STREAM("---------- Laser Detect Option ----------");
    
    // 读取全局配置
    if (root.isMember("global_config")) {
      const Json::Value& global_config = root["global_config"];
      if (global_config.isMember("point_cloud_threshold")) {
        option->point_cloud_threshold = global_config["point_cloud_threshold"].asInt();
        ROS_INFO_STREAM("Global point_cloud_threshold: " << option->point_cloud_threshold);
      }
    }
    
    const Json::Value& detect_option = root["detect"];
    for (const auto& detect : detect_option) {
      RoiArea roi;
      roi.x_min = detect["origin_filter_roi"]["x_min"].asDouble();
      roi.x_max = detect["origin_filter_roi"]["x_max"].asDouble();
      roi.y_min = detect["origin_filter_roi"]["y_min"].asDouble();
      roi.y_max = detect["origin_filter_roi"]["y_max"].asDouble();
      roi.z_min = detect["origin_filter_roi"]["z_min"].asDouble();
      roi.z_max = detect["origin_filter_roi"]["z_max"].asDouble();

      bool save = detect["save"].asBool();
      bool calibration = detect["calibration"].asBool();
      std::string frame = detect["frame"].asString();
      
      // IP address will now be set only from storage.json
      // Region will be set from storage.json

      ROS_INFO_STREAM("------ " << frame << " ------");
      ROS_INFO_STREAM("  save: " << save);
      ROS_INFO_STREAM("  calibration: " << calibration);
      ROS_INFO_STREAM("  x_min: " << roi.x_min);
      ROS_INFO_STREAM("  x_max: " << roi.x_max);
      ROS_INFO_STREAM("  y_min: " << roi.y_min);
      ROS_INFO_STREAM("  y_max: " << roi.y_max);
      ROS_INFO_STREAM("  z_min: " << roi.z_min);
      ROS_INFO_STREAM("  z_max: " << roi.z_max);
      // IP and Region will be set from storage.json later
      ROS_INFO_STREAM("------------");

      option->detect_option[frame].save = save;
      option->detect_option[frame].calibration = calibration;
      option->detect_option[frame].origin_roi_filter = roi;
      // IP and Region will be set from storage.json
    }

  } catch (const std::exception& e) {
    ROS_ERROR_STREAM(e.what());
    ROS_ERROR_STREAM("load detect config failed.");
    return false;
  }

  return true;
}

static bool LoadTfConfig(const std::string& path, LaserStorageOption* option) {
  std::string json_str = GetStringFromFile(path);
  if (json_str.empty()) {
    ROS_ERROR_STREAM(path << " is empty.");
    return false;
  }

  Json::Value root;
  Json::CharReaderBuilder builder;
  std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
  std::string errors;
  if (!reader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errors)) {
    ROS_ERROR("TF config json file data format is error.");
    ROS_ERROR_STREAM("config error info: " << errors);
    return false;
  }

  try {
    ROS_INFO_STREAM("\n");
    ROS_INFO_STREAM("---------- Laser Tf Option ----------");
    const Json::Value& tf_option = root["laser_tf"];
    for (const auto& tf : tf_option) {
      Tf transform;
      transform.x = tf["tf"]["x"].asDouble();
      transform.y = tf["tf"]["y"].asDouble();
      transform.z = tf["tf"]["z"].asDouble();
      transform.roll = tf["tf"]["roll"].asDouble();
      transform.pitch = tf["tf"]["pitch"].asDouble();
      transform.yaw = tf["tf"]["yaw"].asDouble();

      std::string frame = tf["frame"].asString();
      ROS_INFO_STREAM("------ " << frame << " ------");
      ROS_INFO_STREAM("  x: " << transform.x);
      ROS_INFO_STREAM("  y: " << transform.y);
      ROS_INFO_STREAM("  z: " << transform.z);
      ROS_INFO_STREAM("  roll: " << transform.roll);
      ROS_INFO_STREAM("  pitch: " << transform.pitch);
      ROS_INFO_STREAM("  yaw: " << transform.yaw);
      ROS_INFO_STREAM("------------");

      option->tf_option[frame] = transform;
    }

  } catch (const std::exception& e) {
    ROS_ERROR_STREAM(e.what());
    ROS_ERROR_STREAM("load tf config failed.");
    return false;
  }

  return true;
}

static bool LoadCommunicationConfig(const std::string& path, LaserStorageOption* option) {
  std::string json_str = GetStringFromFile(path);
  if (json_str.empty()) {
    ROS_ERROR_STREAM(path << " is empty.");
    return false;
  }

  Json::Value root;
  Json::CharReaderBuilder builder;
  std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
  std::string errors;
  if (!reader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errors)) {
    ROS_INFO_STREAM("path: " << path);
    ROS_ERROR("Communication config json file data format is error.");
    ROS_ERROR_STREAM("config error info: " << errors);
    return false;
  }

  try {
    ROS_INFO_STREAM("\n");
    ROS_INFO_STREAM("---------- Communication Option ----------");
    const Json::Value& communicate_option = root["communicate"];

    option->communicate_option.server_ip = communicate_option["server_ip"].asString();
    ROS_INFO_STREAM("server_ip:" << option->communicate_option.server_ip);

    option->communicate_option.laser_alive_api = communicate_option["laser_alive_api"].asString();
    ROS_INFO_STREAM("laser_alive_api:" << option->communicate_option.laser_alive_api);

    option->communicate_option.laser_alive_check_interval = communicate_option["laser_alive_check_interval"].asDouble();
    ROS_INFO_STREAM("laser_alive_check_interval:" << option->communicate_option.laser_alive_check_interval);

    option->communicate_option.storage_config_api = communicate_option["storage_config_api"].asString();
    ROS_INFO_STREAM("storage_config_api:" << option->communicate_option.storage_config_api);

    option->communicate_option.storage_config_server_ip = communicate_option["storage_config_server_ip"].asString();
    ROS_INFO_STREAM("storage_config_server_ip:" << option->communicate_option.storage_config_server_ip);

    option->communicate_option.storage_config_server_port = communicate_option["storage_config_server_port"].asInt();
    ROS_INFO_STREAM("storage_config_server_port:" << option->communicate_option.storage_config_server_port);

    option->communicate_option.storage_config_retry_count = communicate_option["storage_config_retry_count"].asInt();
    ROS_INFO_STREAM("storage_config_retry_count:" << option->communicate_option.storage_config_retry_count);

    option->communicate_option.storage_config_retry_interval = communicate_option["storage_config_retry_interval"].asDouble();
    ROS_INFO_STREAM("storage_config_retry_interval:" << option->communicate_option.storage_config_retry_interval);

    option->communicate_option.server_port = communicate_option["server_port"].asInt();
    ROS_INFO_STREAM("server_port:" << option->communicate_option.server_port);

    option->communicate_option.local_ip = communicate_option["local_ip"].asString();
    ROS_INFO_STREAM("local_ip:" << option->communicate_option.local_ip);

    option->communicate_option.local_port = communicate_option["local_port"].asInt();
    ROS_INFO_STREAM("local_port:" << option->communicate_option.local_port);

    option->communicate_option.server_api = communicate_option["server_api"].asString();
    ROS_INFO_STREAM("server_api:" << option->communicate_option.server_api);

    option->communicate_option.local_api = communicate_option["local_api"].asString();
    ROS_INFO_STREAM("local_api:" << option->communicate_option.local_api);

    option->communicate_option.update_duration = communicate_option["update_duration"].asDouble();
    ROS_INFO_STREAM("update_duration:" << option->communicate_option.update_duration);

    option->communicate_option.accumulate_duration = communicate_option["accumulate_duration"].asDouble();
    ROS_INFO_STREAM("accumulate_duration:" << option->communicate_option.accumulate_duration);

    option->communicate_option.duration = communicate_option["duration"].asDouble();
    ROS_INFO_STREAM("duration:" << option->communicate_option.duration);

    // Load HA configuration (optional)
    if (communicate_option.isMember("ha")) {
        const Json::Value& ha_option = communicate_option["ha"];
        
        option->communicate_option.enable_ha = ha_option.get("enable", false).asBool();
        ROS_INFO_STREAM("ha.enable:" << option->communicate_option.enable_ha);
        
        if (option->communicate_option.enable_ha) {
            option->communicate_option.vip_address = ha_option.get("vip_address", "").asString();
            ROS_INFO_STREAM("ha.vip_address:" << option->communicate_option.vip_address);
            
            option->communicate_option.ha_interface = ha_option.get("interface", "").asString();
            ROS_INFO_STREAM("ha.interface:" << option->communicate_option.ha_interface);
            
            option->communicate_option.ha_check_interval = ha_option.get("check_interval", 5.0).asDouble();
            ROS_INFO_STREAM("ha.check_interval:" << option->communicate_option.ha_check_interval);
            
            if (option->communicate_option.vip_address.empty()) {
                ROS_ERROR("HA is enabled but vip_address is not specified!");
                return false;
            }
        }
    } else {
        ROS_INFO_STREAM("HA configuration not found, HA mode disabled");
    }

  } catch (const std::exception& e) {
    ROS_ERROR_STREAM(e.what());
    ROS_ERROR_STREAM("load communication config failed.");
    return false;
  }

  return true;
}

static std::string GetStorageConfigFromAPI(const LaserStorageOption& option) {
  try {
    util::RemoteServiceEntry entry(option.communicate_option.storage_config_server_ip, 
                                   option.communicate_option.storage_config_server_port, 
                                   option.communicate_option.storage_config_api);
    
    std::string response = util::RemoteService::GetString(entry);
    if (response.empty()) {
      ROS_ERROR("Failed to get storage config from API: empty response");
      return std::string();
    }
    
    // Parse the response to extract the data field
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
    std::string errors;
    
    if (!reader->parse(response.c_str(), response.c_str() + response.length(), &root, &errors)) {
      ROS_ERROR_STREAM("Failed to parse API response JSON: " << errors);
      return std::string();
    }
    
    // Check if the response has the expected structure
    if (!root.isMember("data")) {
      ROS_ERROR("API response missing 'data' field");
      return std::string();
    }
    
    // Convert the data field back to JSON string
    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, root["data"]);
    
  } catch (const std::exception& e) {
    ROS_ERROR_STREAM("Exception in GetStorageConfigFromAPI: " << e.what());
    return std::string();
  }
}

static bool LoadStorageConfigFromAPI(LaserStorageOption* option, const std::string& storage_path) {
  int retry_count = option->communicate_option.storage_config_retry_count;
  double retry_interval = option->communicate_option.storage_config_retry_interval;
  
  ROS_INFO_STREAM("Attempting to load storage config from API: " 
                  << option->communicate_option.storage_config_server_ip << ":" 
                  << option->communicate_option.storage_config_server_port 
                  << option->communicate_option.storage_config_api);
  
  for (int attempt = 1; attempt <= retry_count; ++attempt) {
    ROS_INFO_STREAM("Storage config fetch attempt " << attempt << "/" << retry_count);
    
    std::string json_str = GetStorageConfigFromAPI(*option);
    
    if (!json_str.empty()) {
      // Try to parse and load the configuration
      Json::Value root;
      Json::CharReaderBuilder builder;
      std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
      std::string errors;
      
      if (reader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errors)) {
        try {
          ROS_INFO_STREAM("\n");
          ROS_INFO_STREAM("---------- Storage Option (from API) ----------");
          
          // Clear existing options
          option->stock_option.clear();
          option->area_stock_id_map.clear();
          
          // Iterate through each device in the array
          for (const auto& device : root) {
            std::string device_name = device["deviceName"].asString();
            std::string device_ip = device["deviceIp"].asString();
            std::string region = device["region"].asString();
            
            ROS_INFO_STREAM("Device: " << device_name << " (IP: " << device_ip << ", Region: " << region << ")");
            
            // Add this device to detect_option even if it has no stocks
            // This ensures all devices can report their alive status
            if (option->detect_option.find(device_name) != option->detect_option.end()) {
              option->detect_option[device_name].region = region;
              option->detect_option[device_name].ip_address = device_ip;
              ROS_INFO_STREAM("  Set IP: " << device_ip << " for device: " << device_name);
            } else {
              // Create an entry for devices without stock detection configuration
              ROS_INFO_STREAM("  Adding device without stock detection: " << device_name);
              option->detect_option[device_name].region = region;
              option->detect_option[device_name].ip_address = device_ip;
              // Set default values for other fields
              option->detect_option[device_name].save = false;
              option->detect_option[device_name].calibration = false;
            }
            
            // Process each stock in the stockList
            const Json::Value& stock_list = device["stockList"];
            for (const auto& stock : stock_list) {
              cotek_laser_detect::StockOption section;
              section.stock_id = stock["nodeNo"].asString();
              section.area_id = stock["areaNo"].asString();
              section.laser_frame = device_name;
              section.detect_fill_level = (stock["detectType"].asInt() == 2);
              
              // Read node position coordinates (previously stock_center)
              section.stock_center.x = stock["nodePosition"]["x"].asDouble();
              section.stock_center.y = stock["nodePosition"]["y"].asDouble();
              
              // Read stock dimensions and orientation
              section.stock_width = stock["stockWidth"].asDouble();
              section.stock_length = stock["stockLength"].asDouble();
              section.stock_yaw = stock["stockYaw"].asDouble();
              section.stock_offset = stock["stockOffset"].asDouble();

              ROS_INFO_STREAM("------ " << section.stock_id << " ------");
              ROS_INFO_STREAM("  stock_id:" << section.stock_id);
              ROS_INFO_STREAM("  area_id:" << section.area_id);
              ROS_INFO_STREAM("  detect_fill_level:" << section.detect_fill_level);
              ROS_INFO_STREAM("  laser_frame:" << section.laser_frame);
              ROS_INFO_STREAM("  stock_center: (" << section.stock_center.x << "," << section.stock_center.y << ")");
              ROS_INFO_STREAM("  stock_width:" << section.stock_width);
              ROS_INFO_STREAM("  stock_length:" << section.stock_length);
              ROS_INFO_STREAM("  stock_yaw:" << section.stock_yaw);
              ROS_INFO_STREAM("  stock_offset:" << section.stock_offset);
              ROS_INFO_STREAM("------------");

              option->stock_option[section.laser_frame].push_back(section);
              option->area_stock_id_map[section.area_id].push_back(section.stock_id);
            }
          }
          
          ROS_INFO_STREAM("Successfully loaded storage config from API");
          ROS_INFO_STREAM("size: " << option->stock_option.size());
          for (auto& laser : option->stock_option) {
            ROS_INFO_STREAM("laser: " << laser.first);
          }
          
          // Backup existing configuration file before saving new one
          std::ifstream check_file(storage_path);
          if (check_file.good()) {
            check_file.close();
            
            // Create backup filename with timestamp
            time_t now = time(0);
            char timestamp[20];
            strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", localtime(&now));
            std::string backup_path = GetBackupFilePath(storage_path, timestamp);
            
            ROS_INFO_STREAM("Backing up existing storage config to: " << backup_path);
            std::string existing_content = GetStringFromFile(storage_path);
            if (!existing_content.empty() && SaveStringToFile(backup_path, existing_content)) {
              ROS_INFO_STREAM("Successfully created backup: " << backup_path);
            } else {
              ROS_WARN_STREAM("Failed to create backup, but will continue with saving new config");
            }
          }
          
          // Save the fetched configuration to local file
          ROS_INFO_STREAM("Saving API-fetched storage config to local file: " << storage_path);
          if (SaveStringToFile(storage_path, json_str)) {
            ROS_INFO_STREAM("Storage config successfully saved to local file");
          } else {
            ROS_WARN_STREAM("Failed to save storage config to local file, but will continue with API config");
          }
          
          return true;
          
        } catch (const std::exception& e) {
          ROS_ERROR_STREAM("Error parsing storage config from API: " << e.what());
        }
      } else {
        ROS_ERROR_STREAM("Failed to parse storage config JSON from API: " << errors);
      }
    }
    
    if (attempt < retry_count) {
      ROS_WARN_STREAM("Storage config fetch failed, retrying in " << retry_interval << " seconds...");
      std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<int>(retry_interval * 1000)));
    }
  }
  
  ROS_ERROR_STREAM("Failed to load storage config from API after " << retry_count << " attempts");
  return false;
}

// Single attempt to load storage config from API (no retry)
static bool LoadStorageConfigFromAPISingleAttempt(LaserStorageOption* option, const std::string& storage_path) {
  ROS_INFO_STREAM("Attempting to load storage config from API (single attempt): " 
                  << option->communicate_option.storage_config_server_ip << ":" 
                  << option->communicate_option.storage_config_server_port 
                  << option->communicate_option.storage_config_api);
  
  std::string json_str = GetStorageConfigFromAPI(*option);
  
  if (json_str.empty()) {
    ROS_ERROR("Failed to get storage config from API: empty response");
    return false;
  }
  
  // Try to parse and load the configuration
  Json::Value root;
  Json::CharReaderBuilder builder;
  std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
  std::string errors;
  
  if (!reader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errors)) {
    ROS_ERROR_STREAM("Failed to parse storage config JSON from API: " << errors);
    return false;
  }
  
  try {
    ROS_INFO_STREAM("\n");
    ROS_INFO_STREAM("---------- Storage Option (from API, single attempt) ----------");
    
    // Clear existing options
    option->stock_option.clear();
    option->area_stock_id_map.clear();
    
    // Iterate through each device in the array
    for (const auto& device : root) {
      std::string device_name = device["deviceName"].asString();
      std::string device_ip = device["deviceIp"].asString();
      std::string region = device["region"].asString();
      
      ROS_INFO_STREAM("Device: " << device_name << " (IP: " << device_ip << ", Region: " << region << ")");
      
      // Add this device to detect_option even if it has no stocks
      // This ensures all devices can report their alive status
      if (option->detect_option.find(device_name) != option->detect_option.end()) {
        option->detect_option[device_name].region = region;
        option->detect_option[device_name].ip_address = device_ip;
        ROS_INFO_STREAM("  Set IP: " << device_ip << " for device: " << device_name);
      } else {
        // Create an entry for devices without stock detection configuration
        ROS_INFO_STREAM("  Adding device without stock detection: " << device_name);
        option->detect_option[device_name].region = region;
        option->detect_option[device_name].ip_address = device_ip;
        // Set default values for other fields
        option->detect_option[device_name].save = false;
        option->detect_option[device_name].calibration = false;
      }
      
      // Process each stock in the stockList
      const Json::Value& stock_list = device["stockList"];
      for (const auto& stock : stock_list) {
        cotek_laser_detect::StockOption section;
        section.stock_id = stock["nodeNo"].asString();
        section.area_id = stock["areaNo"].asString();
        section.laser_frame = device_name;
        section.detect_fill_level = (stock["detectType"].asInt() == 2);
        
        // Read node position coordinates (previously stock_center)
        section.stock_center.x = stock["nodePosition"]["x"].asDouble();
        section.stock_center.y = stock["nodePosition"]["y"].asDouble();
        
        // Read stock dimensions and orientation
        section.stock_width = stock["stockWidth"].asDouble();
        section.stock_length = stock["stockLength"].asDouble();
        section.stock_yaw = stock["stockYaw"].asDouble();
        section.stock_offset = stock["stockOffset"].asDouble();

        ROS_INFO_STREAM("------ " << section.stock_id << " ------");
        ROS_INFO_STREAM("  stock_id:" << section.stock_id);
        ROS_INFO_STREAM("  area_id:" << section.area_id);
        ROS_INFO_STREAM("  detect_fill_level:" << section.detect_fill_level);
        ROS_INFO_STREAM("  laser_frame:" << section.laser_frame);
        ROS_INFO_STREAM("  stock_center: (" << section.stock_center.x << "," << section.stock_center.y << ")");
        ROS_INFO_STREAM("  stock_width:" << section.stock_width);
        ROS_INFO_STREAM("  stock_length:" << section.stock_length);
        ROS_INFO_STREAM("  stock_yaw:" << section.stock_yaw);
        ROS_INFO_STREAM("  stock_offset:" << section.stock_offset);
        ROS_INFO_STREAM("------------");

        option->stock_option[section.laser_frame].push_back(section);
        option->area_stock_id_map[section.area_id].push_back(section.stock_id);
      }
    }
    
    ROS_INFO_STREAM("Successfully loaded storage config from API (single attempt)");
    ROS_INFO_STREAM("size: " << option->stock_option.size());
    for (auto& laser : option->stock_option) {
      ROS_INFO_STREAM("laser: " << laser.first);
    }
    
    // Backup existing configuration file before saving new one
    std::ifstream check_file(storage_path);
    if (check_file.good()) {
      check_file.close();
      
      // Create backup filename with timestamp
      time_t now = time(0);
      char timestamp[20];
      strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", localtime(&now));
      std::string backup_path = GetBackupFilePath(storage_path, timestamp);
      
      ROS_INFO_STREAM("Backing up existing storage config to: " << backup_path);
      std::string existing_content = GetStringFromFile(storage_path);
      if (!existing_content.empty() && SaveStringToFile(backup_path, existing_content)) {
        ROS_INFO_STREAM("Successfully created backup: " << backup_path);
      } else {
        ROS_WARN_STREAM("Failed to create backup, but will continue with saving new config");
      }
    }
    
    // Save the fetched configuration to local file
    ROS_INFO_STREAM("Saving API-fetched storage config to local file: " << storage_path);
    if (SaveStringToFile(storage_path, json_str)) {
      ROS_INFO_STREAM("Storage config successfully saved to local file");
    } else {
      ROS_WARN_STREAM("Failed to save storage config to local file, but will continue with API config");
    }
    
    return true;
    
  } catch (const std::exception& e) {
    ROS_ERROR_STREAM("Error parsing storage config from API: " << e.what());
    return false;
  }
}

static bool LoadStorageConfig(const std::string& path, LaserStorageOption* option) {
  // First try to load from API
  ROS_INFO("Attempting to load storage config from API first...");
  if (LoadStorageConfigFromAPI(option, path)) {
    ROS_INFO("Successfully loaded storage config from API");
    return true;
  }
  
  // Fallback to loading from local file
  ROS_WARN("Failed to load from API, falling back to local storage config file");
  
  std::string json_str = GetStringFromFile(path);
  if (json_str.empty()) {
    ROS_ERROR_STREAM(path << " is empty.");
    return false;
  }

  Json::Value root;
  Json::CharReaderBuilder builder;
  std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
  std::string errors;
  if (!reader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errors)) {
    ROS_INFO_STREAM("path: " << path);
    ROS_ERROR("Storage config json file data format is error.");
    ROS_ERROR_STREAM("config error info: " << errors);
    return false;
  }

  try {
    ROS_INFO_STREAM("\n");
    ROS_INFO_STREAM("---------- Storage Option (from local file) ----------");
    
    // Clear existing options
    option->stock_option.clear();
    option->area_stock_id_map.clear();
    
    // Iterate through each device in the array
    for (const auto& device : root) {
      std::string device_name = device["deviceName"].asString();
      std::string device_ip = device["deviceIp"].asString();
      std::string region = device["region"].asString();
      
      ROS_INFO_STREAM("Device: " << device_name << " (IP: " << device_ip << ", Region: " << region << ")");
      
      // Add this device to detect_option even if it has no stocks
      // This ensures all devices can report their alive status
      if (option->detect_option.find(device_name) != option->detect_option.end()) {
        option->detect_option[device_name].region = region;
        option->detect_option[device_name].ip_address = device_ip;
        ROS_INFO_STREAM("  Set IP: " << device_ip << " for device: " << device_name);
      } else {
        // Create an entry for devices without stock detection configuration
        ROS_INFO_STREAM("  Adding device without stock detection: " << device_name);
        option->detect_option[device_name].region = region;
        option->detect_option[device_name].ip_address = device_ip;
        // Set default values for other fields
        option->detect_option[device_name].save = false;
        option->detect_option[device_name].calibration = false;
      }
      
      // Process each stock in the stockList
      const Json::Value& stock_list = device["stockList"];
      for (const auto& stock : stock_list) {
        cotek_laser_detect::StockOption section;
        section.stock_id = stock["nodeNo"].asString();
        section.area_id = stock["areaNo"].asString();
        section.laser_frame = device_name;
        section.detect_fill_level = (stock["detectType"].asInt() == 2);
        
        // Read node position coordinates (previously stock_center)
        section.stock_center.x = stock["nodePosition"]["x"].asDouble();
        section.stock_center.y = stock["nodePosition"]["y"].asDouble();
        
        // Read stock dimensions and orientation
        section.stock_width = stock["stockWidth"].asDouble();
        section.stock_length = stock["stockLength"].asDouble();
        section.stock_yaw = stock["stockYaw"].asDouble();
        section.stock_offset = stock["stockOffset"].asDouble();

        ROS_INFO_STREAM("------ " << section.stock_id << " ------");
        ROS_INFO_STREAM("  stock_id:" << section.stock_id);
        ROS_INFO_STREAM("  area_id:" << section.area_id);
        ROS_INFO_STREAM("  detect_fill_level:" << section.detect_fill_level);
        ROS_INFO_STREAM("  laser_frame:" << section.laser_frame);
        ROS_INFO_STREAM("  stock_center: (" << section.stock_center.x << "," << section.stock_center.y << ")");
        ROS_INFO_STREAM("  stock_width:" << section.stock_width);
        ROS_INFO_STREAM("  stock_length:" << section.stock_length);
        ROS_INFO_STREAM("  stock_yaw:" << section.stock_yaw);
        ROS_INFO_STREAM("  stock_offset:" << section.stock_offset);
        ROS_INFO_STREAM("------------");

        option->stock_option[section.laser_frame].push_back(section);
        option->area_stock_id_map[section.area_id].push_back(section.stock_id);
      }
    }

  } catch (const std::exception& e) {
    ROS_ERROR_STREAM(e.what());
    ROS_ERROR_STREAM("load storage config failed.");
    return false;
  }

  ROS_INFO_STREAM("Successfully loaded storage config from local file");
  ROS_INFO_STREAM("size: " << option->stock_option.size());
  for (auto& laser : option->stock_option) {
    ROS_INFO_STREAM("laser: " << laser.first);
  }

  return true;
}

#endif  // INCLUDE_CONFIG_H_

/**
 * Copyright (c) 2022 CoTEK Inc. All rights reserved.
 */
#ifndef INCLUDE_STOCK_H_
#define INCLUDE_STOCK_H_

#include <json/json.h>

#include <atomic>
#include <fstream>
#include <iostream>
#include <memory>
#include <mutex>

#include "cotek_enum.h"
namespace cotek_laser_detect {

class StockStateQueue : public std::deque<StockState> {
 public:
  explicit StockStateQueue(const uint8_t &filter_num, const bool &detect_fill_level)
      : filter_num_(filter_num), detect_fill_level_(detect_fill_level)   {}
  void PushData(const StockState &state);

  StockState GetData();

 private:
  uint8_t filter_num_;
  bool detect_fill_level_;
  std::mutex mutex_;
};

class Stock {
 public:
  Stock() = delete;
  explicit Stock(const StockOption &option);
  ~Stock() {}

  void updateStockState(StockState state);
  void updateHeightScore(float score);

  inline StockState get_state() { return state_queue_.GetData(); }
  inline std::string get_id() const { return option_.stock_id; }
  inline StockOption option() const { return option_; }
  inline float get_height_score() const { return height_score_; }

 private:
  bool CreateDirectoryRecursive(const std::string& path, mode_t mode = 0755);

  StockOption option_;
  StockStateQueue state_queue_;
  std::chrono::steady_clock::time_point last_save_time_;
  float height_score_ = 0.0f;

  std::mutex mutex_;
};
}  // namespace cotek_cam_detect

#endif  // INCLUDE_STOCK_H_

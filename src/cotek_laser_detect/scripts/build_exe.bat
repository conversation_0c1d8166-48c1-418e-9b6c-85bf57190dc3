@echo off
echo ========================================
echo  Lidar Calibration Tool - EXE Builder
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

echo [1/5] Installing required packages...
pip install pyinstaller PyQt5 PyYAML pillow

echo.
echo [2/5] Creating application icon...
python create_icon.py

echo.
echo [3/5] Cleaning previous build...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "LidarCalibTool.exe" del "LidarCalibTool.exe"

echo.
echo [4/5] Building executable (this may take 2-3 minutes)...
echo Please wait...

REM Try building with spec file first (includes version info and icon)
if exist "lidar_calib_tool.spec" (
    echo Using spec file for advanced build...
    pyinstaller lidar_calib_tool.spec
) else (
    echo Using direct build...
    if exist "icon.ico" (
        pyinstaller --onefile --windowed --name="LidarCalibTool" --version-file="version_info.txt" --icon="icon.ico" lidar_calib_tool.py
    ) else (
        pyinstaller --onefile --windowed --name="LidarCalibTool" --version-file="version_info.txt" lidar_calib_tool.py
    )
)

if %errorlevel% neq 0 (
    echo ERROR: Failed to build executable
    echo Trying fallback build method...
    pyinstaller --onefile --windowed --name="LidarCalibTool" lidar_calib_tool.py
    if %errorlevel% neq 0 (
        echo ERROR: All build methods failed
        pause
        exit /b 1
    )
)

echo.
echo [5/5] Finalizing...

REM Copy the executable to current directory for convenience
if exist "dist\LidarCalibTool.exe" (
    copy "dist\LidarCalibTool.exe" "LidarCalibTool.exe" >nul
    echo.
    echo ========================================
    echo  BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Executable created: LidarCalibTool.exe
    echo File size:
    for %%A in (LidarCalibTool.exe) do echo   %%~zA bytes
    echo.
    echo You can now:
    echo 1. Run LidarCalibTool.exe directly (no Python needed)
    echo 2. Copy LidarCalibTool.exe to any Windows computer
    echo 3. Create shortcuts or distribute the file
    echo.
) else (
    echo ERROR: Executable not found in dist folder
    pause
    exit /b 1
)

echo Cleaning up temporary files...
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "__pycache__" rmdir /s /q "__pycache__" >nul 2>&1

echo.
echo Press any key to exit...
pause >nul

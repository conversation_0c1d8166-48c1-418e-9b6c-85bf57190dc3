@echo off
echo Installing dependencies for Lidar Calibration Tool on Windows...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

echo Python is installed. Installing required packages...
echo.

REM Install PyQt5
echo Installing PyQt5...
pip install PyQt5
if %errorlevel% neq 0 (
    echo ERROR: Failed to install PyQt5
    pause
    exit /b 1
)

REM Install PyYAML
echo Installing PyYAML...
pip install PyYAML
if %errorlevel% neq 0 (
    echo ERROR: Failed to install PyYAML
    pause
    exit /b 1
)

echo.
echo All dependencies installed successfully!
echo You can now run the Lidar Calibration Tool by double-clicking run_lidar_calib_tool.bat
echo.
pause

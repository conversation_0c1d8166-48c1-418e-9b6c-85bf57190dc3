# 激光雷达标定工具 - Windows使用指南

## 🚀 快速开始 (推荐)

### 方法一：使用预编译的EXE文件
如果您有现成的 `LidarCalibTool.exe` 文件：
1. 直接双击 `LidarCalibTool.exe` 运行
2. 无需安装Python或任何依赖包
3. 可以在任何Windows电脑上运行

### 方法二：自己打包EXE文件
1. 确保已安装Python 3.7+
2. 双击运行 `build_exe.bat` 或 `quick_build.bat`
3. 等待打包完成（2-3分钟）
4. 运行生成的 `LidarCalibTool.exe`

## 📋 详细安装步骤

### 系统要求
- Windows 7/8/10/11
- Python 3.7 或更高版本（仅打包时需要）

### 打包为EXE文件（一次性操作）

#### 简单方法：
1. 双击运行 `quick_build.bat`
2. 等待完成，即可获得 `LidarCalibTool.exe`

#### 完整方法（包含图标和版本信息）：
1. 双击运行 `build_exe.bat`
2. 脚本会自动：
   - 安装所需依赖包
   - 创建应用程序图标
   - 打包成exe文件
   - 清理临时文件

### 传统安装方法

### 1. 安装Python
如果您的系统还没有安装Python：
1. 访问 https://www.python.org/downloads/
2. 下载最新版本的Python
3. 运行安装程序，**确保勾选"Add Python to PATH"**

### 2. 安装依赖包
双击运行 `install_dependencies_windows.bat` 文件，它会自动安装所需的依赖包：
- PyQt5 (图形界面库)
- PyYAML (YAML文件处理库)

### 3. 运行程序
双击运行 `run_lidar_calib_tool.bat` 文件启动激光雷达标定工具。

## 手动运行
如果批处理文件无法正常工作，您也可以手动运行：

1. 打开命令提示符（cmd）或PowerShell
2. 导航到脚本所在目录
3. 运行命令：
   ```
   python lidar_calib_tool.py
   ```

## 功能说明
- **激光雷达编号输入**：输入要标定的激光雷达编号
- **偏移方向选择**：选择1.428距离的偏移方向（上/下/左/右）
- **快速坐标输入**：支持批量粘贴激光雷达坐标和世界坐标
- **手动坐标输入**：支持手动输入3个对应点的坐标
- **YAML生成**：自动生成标定用的YAML配置文件

## 输出文件
生成的YAML文件将保存在 `yaml_calib` 目录下，文件名格式为 `lidar{编号}.yaml`

## 📁 文件说明

- `LidarCalibTool.exe` - 主程序（打包后生成）
- `build_exe.bat` - 完整打包脚本（推荐）
- `quick_build.bat` - 快速打包脚本
- `lidar_calib_tool.py` - 源代码
- `create_icon.py` - 图标生成脚本
- `version_info.txt` - 版本信息文件
- `lidar_calib_tool.spec` - PyInstaller配置文件

## 🎯 EXE文件优势

- ✅ **无需Python环境** - 可在任何Windows电脑运行
- ✅ **单文件分发** - 只需一个exe文件
- ✅ **快速启动** - 双击即可运行
- ✅ **专业外观** - 包含图标和版本信息
- ✅ **便于分发** - 可通过邮件、U盘等方式分享

## 故障排除

### 打包相关问题

**打包失败：**
- 确保Python已正确安装
- 尝试运行 `quick_build.bat` 进行简化打包
- 检查是否有杀毒软件阻止

**exe文件过大：**
- 正常情况下文件大小约50-100MB
- 这是因为包含了完整的Python运行时

**exe启动慢：**
- 首次启动可能需要几秒钟解压
- 后续启动会更快

### 中文显示问题
程序已针对Windows系统优化了中文字体显示，使用Microsoft YaHei字体。

### 依赖包安装失败
如果自动安装失败，请手动运行：
```
pip install PyQt5 PyYAML
```

### Python未找到错误
确保Python已正确安装并添加到系统PATH中。可以在命令提示符中运行 `python --version` 验证。

## 联系支持
如有问题，请联系技术支持团队。

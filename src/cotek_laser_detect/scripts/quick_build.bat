@echo off
echo Quick Build - Lidar Calibration Tool
echo.

REM Install dependencies and build in one go
pip install pyinstaller PyQt5 PyYAML

echo Building executable...
pyinstaller --onefile --windowed --name="LidarCalibTool" lidar_calib_tool.py

if exist "dist\LidarCalibTool.exe" (
    copy "dist\LidarCalibTool.exe" "LidarCalibTool.exe"
    echo.
    echo SUCCESS! LidarCalibTool.exe is ready to use.
    echo You can now run it directly without Python.
) else (
    echo Build failed. Please check for errors above.
)

pause

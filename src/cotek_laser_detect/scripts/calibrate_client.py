#coding: utf-8
#!/usr/bin/env python
from __future__ import print_function  # 允许使用 print() 函数

import rospy
import yaml
import sys
from cotek_laser_detect.srv import calibrate, calibrateRequest
from geometry_msgs.msg import Point

def load_points(file_path):
    """加载YAML格式的点对数据并提取设备名"""
    with open(file_path, 'r') as f:
        data = yaml.safe_load(f)
    
    # 获取设备名（YAML根节点名称）
    device_name = data.keys()[0]  # Python2中dict.keys()返回列表
    points_data = data[device_name]['points']
    
    points_a = []
    points_b = []
    for pair in points_data:
        # 提取lidar和world坐标点
        a = Point(*pair['lidar_point'])
        b = Point(*pair['world_point'])
        points_a.append(a)
        points_b.append(b)
    
    return device_name, points_a, points_b

def calibrate_client(file_path):
    # 加载数据并获取设备名
    device_name, points_a, points_b = load_points(file_path)
    
    # 动态生成服务名称
    service_name = 'calibrate_{}'.format(device_name)
    print("waiting service calibrate_{}".format(device_name))
    rospy.wait_for_service(service_name)
    
    try:
        # 创建服务代理
        service = rospy.ServiceProxy(service_name, calibrate)
        req = calibrateRequest()
        req.points_a = points_a
        req.points_b = points_b

        # 数据验证
        if len(req.points_a) != len(req.points_b):
            raise ValueError("点对数量不匹配")
        if not req.points_a:  # 空列表判断
            raise ValueError("空数据")

        # 调用服务
        response = service(req)
        # 格式化输出结果
        result = '成功' if response.success else '失败'
        print("校准结果: {}".format(result))
        return response.success
    except rospy.ServiceException as e:
        print("服务调用失败: {}".format(e))
        return False
    except Exception as e:
        print("发生错误: {}".format(e))
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: ./calibrate_client.py 数据文件.yaml")
        sys.exit(1)
        
    calibrate_client(sys.argv[1])
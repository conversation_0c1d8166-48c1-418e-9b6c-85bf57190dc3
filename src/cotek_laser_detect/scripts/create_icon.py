#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Create a simple icon for the Lidar Calibration Tool
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    def create_icon():
        # Create a 256x256 image with transparent background
        size = 256
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw a simple radar-like icon
        center = size // 2
        
        # Draw outer circle (radar range)
        circle_color = (0, 150, 255, 255)  # Blue
        draw.ellipse([20, 20, size-20, size-20], outline=circle_color, width=8)
        
        # Draw inner circles
        draw.ellipse([60, 60, size-60, size-60], outline=circle_color, width=4)
        draw.ellipse([100, 100, size-100, size-100], outline=circle_color, width=2)
        
        # Draw radar sweep lines
        line_color = (255, 100, 0, 255)  # Orange
        draw.line([center, center, center + 100, center - 100], fill=line_color, width=6)
        draw.line([center, center, center - 70, center - 70], fill=line_color, width=4)
        
        # Draw center point
        draw.ellipse([center-8, center-8, center+8, center+8], fill=(255, 0, 0, 255))
        
        # Save as ICO file
        icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        img.save('icon.ico', format='ICO', sizes=icon_sizes)
        print("Icon created successfully: icon.ico")
        
    if __name__ == "__main__":
        create_icon()
        
except ImportError:
    print("PIL (Pillow) not installed. Creating a simple text-based icon...")
    # Create a minimal ICO file without PIL
    # This is a very basic approach - in practice you'd want a proper icon
    with open('icon.ico', 'wb') as f:
        # Write minimal ICO header (this won't be a real icon, just a placeholder)
        f.write(b'\x00\x00\x01\x00\x01\x00\x10\x10\x00\x00\x01\x00\x08\x00h\x05\x00\x00\x16\x00\x00\x00')
        f.write(b'\x00' * 1384)  # Minimal icon data
    print("Basic icon placeholder created: icon.ico")

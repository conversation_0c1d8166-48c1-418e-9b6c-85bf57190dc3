#include "laser_detect.h"
#include "common.h"
#include "calibration.h"
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/common/common.h>
#include <pcl/common/io.h>
#include <pcl/common/transforms.h>
#include <pcl/filters/crop_box.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/filters/passthrough.h>
#include <pcl/filters/radius_outlier_removal.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/search/kdtree.h>
#include <pcl/segmentation/extract_clusters.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/segmentation/sac_segmentation.h>
#include <pcl/io/pcd_io.h>
#include <geometry_msgs/Point.h>
#include <ros/package.h>
#include <vector>
#include <cmath>
#include <algorithm>
#include <limits>
#include <json/json.h>
#include <chrono>
#include <iomanip>
#include <sstream>

#define MAX_NUM 15

namespace cotek_laser_detect {

// 添加库位坐标缓存结构
struct StockBoundary {
    Point min_corner;
    Point max_corner;
    double z_min;
    double z_max;
    
    StockBoundary(const Point& min_pt, const Point& max_pt, double z_min_val, double z_max_val)
        : min_corner(min_pt), max_corner(max_pt), z_min(z_min_val), z_max(z_max_val) {}
};

template <typename T>
std::string GetCurrentTime() {
  std::chrono::time_point<std::chrono::system_clock, std::chrono::milliseconds>
      tp = std::chrono::time_point_cast<std::chrono::milliseconds>(
          std::chrono::system_clock::now());
  auto tmp = std::chrono::duration_cast<std::chrono::milliseconds>(
      tp.time_since_epoch());
  return std::to_string(tmp.count());
}

static std::pair<Point, Point> CalculateStockCorners(const StockOption& stock) {
    // Calculate the true center of the stock
    double center_angle_rad = stock.stock_yaw * M_PI / 180.0;
    Point true_center;
    true_center.x = stock.stock_center.x + stock.stock_offset * cos(center_angle_rad);
    true_center.y = stock.stock_center.y + stock.stock_offset * sin(center_angle_rad);

    // Half dimensions
    double half_width = stock.stock_width / 2.0;
    double half_length = stock.stock_length / 2.0;

    // If the stock is a fill level stock, use the 75% of the width and length  
    if (stock.detect_fill_level){
      half_width = stock.stock_width * 0.75 / 2.0;
      half_length = stock.stock_length * 0.75 / 2.0;
    }

    // Calculate direction vectors
    double cos_theta = cos(center_angle_rad);
    double sin_theta = sin(center_angle_rad);

    // Calculate four vertices of the rotated rectangle
    Point vertices[4];
    
    // Main direction components
    double main_dir_x = cos_theta * half_length;
    double main_dir_y = sin_theta * half_length;
    
    // Width direction components (perpendicular to main direction)
    double width_dir_x = sin_theta * half_width;
    double width_dir_y = -cos_theta * half_width;

    // Generate all four vertices
    vertices[0].x = true_center.x + main_dir_x + width_dir_x;
    vertices[0].y = true_center.y + main_dir_y + width_dir_y;
    
    vertices[1].x = true_center.x + main_dir_x - width_dir_x;
    vertices[1].y = true_center.y + main_dir_y - width_dir_y;
    
    vertices[2].x = true_center.x - main_dir_x + width_dir_x;
    vertices[2].y = true_center.y - main_dir_y + width_dir_y;
    
    vertices[3].x = true_center.x - main_dir_x - width_dir_x;
    vertices[3].y = true_center.y - main_dir_y - width_dir_y;

    // Find bounding box coordinates
    double min_x = vertices[0].x;
    double max_x = vertices[0].x;
    double min_y = vertices[0].y;
    double max_y = vertices[0].y;

    for(int i = 1; i < 4; ++i) {
        if(vertices[i].x < min_x) min_x = vertices[i].x;
        if(vertices[i].x > max_x) max_x = vertices[i].x;
        if(vertices[i].y < min_y) min_y = vertices[i].y;
        if(vertices[i].y > max_y) max_y = vertices[i].y;
    }

    return {{min_x, min_y}, {max_x, max_y}};
}

static pcl::PointXYZ GetPoint(const std::vector<pcl::PointXYZ>& p_vec) {
  if (p_vec.empty()) {
    return pcl::PointXYZ(0, 0, 0);  // 返回默认点
  }
  
  // 使用更高效的累加方式计算中心点
  double sum_x = 0.0, sum_y = 0.0, sum_z = 0.0;
  const size_t n = p_vec.size();
  
  // 直接遍历向量，避免额外的引用开销
  for (size_t i = 0; i < n; ++i) {
    sum_x += p_vec[i].x;
    sum_y += p_vec[i].y;
    sum_z += p_vec[i].z;
  }
  
  // 计算平均值
  return pcl::PointXYZ(
    static_cast<float>(sum_x / n), 
    static_cast<float>(sum_y / n), 
    static_cast<float>(sum_z / n)
  );
}

static void RoiFilterCloud(pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_ptr,
                           const double& z_min, const double& z_max,
                           const double& x_min, const double& x_max,
                           const double& y_min, const double& y_max,
                           const bool& enable_filter) {
  pcl::PassThrough<pcl::PointXYZ> pass;

  pass.setInputCloud(cloud_ptr);
  pass.setFilterFieldName("x");
  pass.setFilterLimits(x_min, x_max);
  pass.filter(*cloud_ptr);

  pass.setInputCloud(cloud_ptr);
  pass.setFilterFieldName("y");
  pass.setFilterLimits(y_min, y_max);
  pass.filter(*cloud_ptr);

  pass.setInputCloud(cloud_ptr);
  pass.setFilterFieldName("z");
  pass.setFilterLimits(z_min, z_max);
  pass.filter(*cloud_ptr);

  if (enable_filter) {
    pcl::StatisticalOutlierRemoval<pcl::PointXYZ> sor; //remove the outlier
    sor.setInputCloud(cloud_ptr);
    sor.setMeanK(5); //K近邻搜索点个数
    sor.setStddevMulThresh(1.0); //标准差倍数
    sor.setNegative(false); //保留未滤波点（内点）
    sor.filter(*cloud_ptr);  //保存滤波结果到cloud_filter

    pcl::RadiusOutlierRemoval<pcl::PointXYZ> pcFilter; //创建滤波器对象
    pcFilter.setInputCloud(cloud_ptr); //设置待滤波的点云
    pcFilter.setRadiusSearch(0.04); // 设置搜索半径
    pcFilter.setMinNeighborsInRadius(20); // 设置一个内点最少的邻点数目
    pcFilter.filter(*cloud_ptr); //滤波结果存储到cloud_filtered

    sor.setInputCloud(cloud_ptr);
    sor.setMeanK(5); //K近邻搜索点个数
    sor.setStddevMulThresh(0.5); //标准差倍数
    sor.setNegative(false); //保留未滤波点（内点）
    sor.filter(*cloud_ptr);  //保存滤波结果到cloud_filter
  }
}

// 高效的ROI过滤方法，返回过滤后的点云
static pcl::PointCloud<pcl::PointXYZ>::Ptr RoiFilterCloudEfficient(
    const pcl::PointCloud<pcl::PointXYZ>::ConstPtr& input_cloud,
    const double& z_min, const double& z_max,
    const double& x_min, const double& x_max,
    const double& y_min, const double& y_max) {
  
  pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZ>);
  
  // 预估过滤后的点云大小，减少内存重新分配
  filtered_cloud->reserve(input_cloud->size() / 4);
  
  // 直接遍历点云进行过滤，避免多次PassThrough操作
  for (const auto& point : input_cloud->points) {
    if (point.x >= x_min && point.x <= x_max &&
        point.y >= y_min && point.y <= y_max &&
        point.z >= z_min && point.z <= z_max) {
      filtered_cloud->push_back(point);
    }
  }
  
  filtered_cloud->width = filtered_cloud->size();
  filtered_cloud->height = 1;
  filtered_cloud->is_dense = true;
  
  return filtered_cloud;
}

static void print_working_dir() {
    char buffer[256];
    if (getcwd(buffer, sizeof(buffer)) != nullptr) {
        std::cout << "当前工作目录: " << buffer << std::endl;
    }
}

static void saveCalibrationParams(const std::string& frame, const cotek_laser_detect::CalibrationParams& params, 
                                  const std::string& filename) {
    // 读取或初始化JSON数据
    std::string json_str;
    std::ifstream in_file(filename);
    if (in_file.good()) {
        in_file.seekg(0, std::ios::end);
        size_t size = in_file.tellg();
        in_file.seekg(0, std::ios::beg);
        json_str.resize(size);
        in_file.read(&json_str[0], size);
        in_file.close();
    } else {
        ROS_ERROR("tf.json path error");
        json_str = R"({"laser_tf": []})";
        return;
    }

    // 解析JSON
    Json::Value root;
    Json::CharReaderBuilder builder;
    builder["collectComments"] = false;
    std::string errs;
    std::istringstream iss(json_str);
    if (!Json::parseFromStream(builder, iss, &root, &errs)) {
        ROS_ERROR("Failed to parse JSON: %s", errs.c_str());
        return;
    }
    
    // 处理解析错误或无效结构
    if (!root.isMember("laser_tf") || !root["laser_tf"].isArray()) {
        root["laser_tf"] = Json::Value(Json::arrayValue);
    }

    // 获取laser_tf数组
    Json::Value& laser_tf = root["laser_tf"];
    bool found = false;

    // 遍历查找匹配的frame
    for (Json::Value& entry : laser_tf) {
        if (entry["frame"].asString() == frame) {
            // 创建新的tf对象
            Json::Value tf_obj;
            tf_obj["x"] = params.tx;
            tf_obj["y"] = params.ty;
            tf_obj["z"] = params.tz;
            tf_obj["roll"] = params.roll;
            tf_obj["pitch"] = params.pitch;
            tf_obj["yaw"] = params.yaw;
            
            // 更新条目
            entry["tf"] = tf_obj;
            found = true;
            break;
        }
    }

    // 如果没有找到则添加新条目
    if (!found) {
        Json::Value new_entry;
        new_entry["frame"] = frame;
        
        Json::Value tf_obj;
        tf_obj["x"] = params.tx;
        tf_obj["y"] = params.ty;
        tf_obj["z"] = params.tz;
        tf_obj["roll"] = params.roll;
        tf_obj["pitch"] = params.pitch;
        tf_obj["yaw"] = params.yaw;
        
        new_entry["tf"] = tf_obj;
        laser_tf.append(new_entry);
    }

    // 写入文件
    std::ofstream out_file(filename);
    if (out_file.is_open()) {
        try {
            Json::StreamWriterBuilder builder;
            builder["indentation"] = "  ";  // 设置缩进为2个空格
            std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
            writer->write(root, &out_file);
            out_file.close();
            
            if (out_file.fail()) {
                ROS_ERROR_STREAM("Write operation failed for file: " << filename << " (disk full?)");
            } else {
                ROS_WARN_STREAM("Successfully saved parameters to " << filename);
            }
        } catch (const std::exception& e) {
            ROS_ERROR_STREAM("Exception during file write: " << e.what());
        }
    } else {
        ROS_ERROR_STREAM("Failed to open file " << filename << " for writing");
        ROS_WARN_STREAM("Calibration parameters NOT saved for frame: " << frame);
    }
}


bool LaserDetector::Init() {
  ros::NodeHandle nh;

  // cloud_pub_ = nh.advertise<sensor_msgs::PointCloud2>(frame_+"/roi", 1);

  // Generate topic name based on IP address
  std::string topic_name;
  if (option_.detect_option.find(frame_) == option_.detect_option.end() || 
      option_.detect_option[frame_].ip_address.empty()) {
    ROS_ERROR_STREAM(frame_ << " IP address not found in configuration! Please check storage.json");
    return false;
  }
  
  // Get IP address and convert dots to underscores
  std::string ip = option_.detect_option[frame_].ip_address;
  std::replace(ip.begin(), ip.end(), '.', '_');
  topic_name = "livox/lidar_" + ip;

  sub_.emplace_back(nh.subscribe<sensor_msgs::PointCloud2>(
      topic_name, 10,
      boost::bind(&LaserDetector::LaserCallBack, this, _1)));

  ROS_INFO_STREAM(frame_ << " register callback for topic: " << topic_name);

  detect_option_ = option_.detect_option[frame_];
  
  // Check if the device has transform parameters in the config
  if (option_.tf_option.find(frame_) != option_.tf_option.end()) {
    tf_ = option_.tf_option[frame_];
  } else {
    // Set default transform values if not found
    tf_.x = tf_.y = tf_.z = 0.0;
    tf_.roll = tf_.pitch = tf_.yaw = 0.0;
    ROS_INFO_STREAM(frame_ << " using default transform (no detection, only alive status)");
  }
  
  // Check if this device has any stocks to monitor
  if (option_.stock_option.find(frame_) != option_.stock_option.end()) {
    stock_option_ = option_.stock_option[frame_];
  } else {
    // No stocks for this device
    ROS_INFO_STREAM(frame_ << " has no stocks to monitor (only alive status)");
  }

  if (detect_option_.calibration){
    // 开启服务
    calibrate_srv_ = nh.advertiseService(
        "calibrate_" + frame_, &LaserDetector::CalibrateRequest, this);
  }

  // 根据detect.json中的save配置决定是否开启保存检测点云的服务
  if (detect_option_.save) {
    save_detection_cloud_srv_ = nh.advertiseService(
        "save_detection_cloud_" + frame_, &LaserDetector::SaveDetectionCloudRequest, this);
    ROS_INFO_STREAM(frame_ << " save detection cloud service enabled");
  }

  calib_params_.pitch = tf_.pitch;
  calib_params_.roll = tf_.roll;
  calib_params_.yaw = tf_.yaw;
  calib_params_.tx = tf_.x;
  calib_params_.ty = tf_.y;
  calib_params_.tz = tf_.z;

  std::cout << "[x, y, z]: " << tf_.x << ", " << tf_.y << ", " << tf_.z << std::endl;
  std::cout << "[roll, pitch, yaw]: " << tf_.roll << ", " << tf_.pitch << ", " << tf_.yaw << std::endl;

  saved_pcd_path_ = option_.package_path + "/config/calibrate/" + frame_ + ".pcd";
  tf_path_ = option_.package_path + "/config/tf.json";
  save_time_ = ros::Time::now() - ros::Duration(10.);

  accumulated_cloud_ = boost::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
  accumulated_cloud_updated_ = false;
  shutdown_flag_ = false;

  get_frame_num_ = 0;
  request_calibrate_ = false;

  // Initialize last_data_received_ to current time minus timeout
  // This ensures it shows as offline until first data is received
  last_data_received_ = ros::Time::now() - ros::Duration(option_.communicate_option.update_duration * 3);

  MakeValidStockMap();

  Run();

  return true;
}

bool LaserDetector::MakeValidStockMap() {
  // Skip processing if there are no stocks to monitor
  if (stock_option_.empty()) {
    ROS_INFO_STREAM(frame_ << " has no stocks to map, will only report alive status");
    return true;
  }
  
  for (const auto& stock : stock_option_){
    area_stocks_[stock.area_id].push_back(stock.stock_id);
    id_stocks_[stock.stock_id] = std::make_shared<Stock>(stock);
  }
  
  // 预计算库位边界坐标
  PrecomputeStockBoundaries();
  
  return true;
}

void LaserDetector::PrecomputeStockBoundaries() {
  // 清空缓存
  stock_boundaries_cache_.clear();
  stock_z_ranges_cache_.clear();
  
  for (const auto& stock : stock_option_) {
    // 计算库位边界坐标
    auto [stock_min_corner, stock_max_corner] = CalculateStockCorners(stock);
    
    // 计算Z轴范围
    double z_min = 0.2, z_max = 2.8;
    if (stock.detect_fill_level) {
      z_min = 0.1;
    }
    
    // 缓存结果
    stock_boundaries_cache_[stock.stock_id] = {stock_min_corner, stock_max_corner};
    stock_z_ranges_cache_[stock.stock_id] = {z_min, z_max};
    
    ROS_INFO_STREAM("Precomputed boundaries for stock " << stock.stock_id 
                    << ": x[" << stock_min_corner.x << "," << stock_max_corner.x << "]"
                    << ", y[" << stock_min_corner.y << "," << stock_max_corner.y << "]"
                    << ", z[" << z_min << "," << z_max << "]");
  }
  
  ROS_INFO_STREAM("Precomputed boundaries for " << stock_boundaries_cache_.size() << " stocks");
}

void LaserDetector::LaserCallBack(const sensor_msgs::PointCloud2::ConstPtr &msg) {
  std::string frame = msg->header.frame_id;
  //std::cout << frame_ << " callback" << std::endl;

  // 在HA模式下，如果是备机则不处理点云数据
  if (ha_mode_enabled_ && !is_master_) {
    // 备机模式：不处理点云数据，但更新接收时间戳以表明激光器硬件连接正常
    // 但在IsAlive()中会返回false，因为备机不应该被认为是存活的
    last_data_received_ = ros::Time::now();
    return;
  }

  std::lock_guard<std::mutex> lock(mutex_);
  vaild_cloud_.vaild = true;
  pcl::fromROSMsg(*msg, vaild_cloud_.cloud);
  
  // Update last received data timestamp
  last_data_received_ = ros::Time::now();
}

void LaserDetector::Run() {
  // 启动点云积累线程
  accumulator_ = 
      std::make_shared<std::thread>(std::bind(&LaserDetector::AccumulateProcess, this));
  
  // 启动检测线程
  executor_ = 
      std::make_shared<std::thread>(std::bind(&LaserDetector::Runner, this));
}

void LaserDetector::AccumulateProcess() {
  double accumulate_duration = option_.communicate_option.accumulate_duration;

  // 检查合法性，避免除零或负数
  if (accumulate_duration <= 0) {
    ROS_ERROR("Invalid accumulate_duration %.2f seconds, using default 1 seconds.", accumulate_duration);
    accumulate_duration = 1; // 默认1秒
  }

  // 设置点云处理频率
  ros::Rate rate(1.0 / accumulate_duration);
  
  // 初始化队列和累积点云
  std::queue<pcl::PointCloud<pcl::PointXYZ>::Ptr> local_cloud_queue;
  
  while (ros::ok() && !shutdown_flag_) {
    // 获取新点云
    pcl::PointCloud<pcl::PointXYZ> new_cloud;
    bool has_new_cloud = false;
    
    {
      std::lock_guard<std::mutex> lock(mutex_);
      if (vaild_cloud_.vaild) {
        new_cloud = vaild_cloud_.cloud;
        vaild_cloud_.vaild = false;
        has_new_cloud = true;
      }
    }
    
    if (!has_new_cloud) {
      rate.sleep();
      continue;
    }
    
    if (new_cloud.empty()) {
      rate.sleep();
      continue;
    }
    
    // 先进行ROI过滤，减少需要处理的点云数量
    pcl::PointCloud<pcl::PointXYZ>::ConstPtr new_cloud_ptr(new pcl::PointCloud<pcl::PointXYZ>(new_cloud));
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_ptr = RoiFilterCloudEfficient(
        new_cloud_ptr,
        detect_option_.origin_roi_filter.z_min, 
        detect_option_.origin_roi_filter.z_max,
        detect_option_.origin_roi_filter.x_min,
        detect_option_.origin_roi_filter.x_max, 
        detect_option_.origin_roi_filter.y_min, 
        detect_option_.origin_roi_filter.y_max);
    
    // 如果过滤后点云为空，跳过这一帧
    if (cloud_ptr->empty()) {
      rate.sleep();
      continue;
    }
    
    // 坐标变换
    Eigen::Matrix3f R = 
        Eigen::AngleAxisf(calib_params_.yaw, Eigen::Vector3f::UnitZ()) *
        Eigen::AngleAxisf(calib_params_.pitch, Eigen::Vector3f::UnitY()) *
        Eigen::AngleAxisf(calib_params_.roll, Eigen::Vector3f::UnitX())
        .matrix();
    
    Eigen::Affine3f transform = Eigen::Translation3f(calib_params_.tx, calib_params_.ty, calib_params_.tz) * R;
    try {
        pcl::transformPointCloud(*cloud_ptr, *cloud_ptr, transform);
    } catch (const std::exception& e) {
        ROS_ERROR_STREAM("PCL transform failed: " << e.what());
        return;
    }
    
    // 更新本地队列
    local_cloud_queue.push(cloud_ptr);
    
    // 维护滑动窗口：如果队列超过最大大小，移除最老的点云
    if (local_cloud_queue.size() > MAX_NUM) {
      local_cloud_queue.pop();
    }
    
    // 当队列达到最大大小时，重新累积所有点云
    if (local_cloud_queue.size() == MAX_NUM) {
      // 创建新的累积点云
      pcl::PointCloud<pcl::PointXYZ>::Ptr local_accumulated_cloud(new pcl::PointCloud<pcl::PointXYZ>);
      
      // 复制队列以进行迭代
      std::queue<pcl::PointCloud<pcl::PointXYZ>::Ptr> temp_queue = local_cloud_queue;
      
      // 累积所有点云
      while (!temp_queue.empty()) {
        *local_accumulated_cloud += *(temp_queue.front());
        temp_queue.pop();
      }
      
      // 设置点云属性
      local_accumulated_cloud->width = local_accumulated_cloud->points.size();
      local_accumulated_cloud->height = 1;
      local_accumulated_cloud->is_dense = true;
      
      // 安全地更新共享点云
      {
        std::lock_guard<std::mutex> lock(accumulated_cloud_mutex_);
        accumulated_cloud_ = local_accumulated_cloud;
        accumulated_cloud_updated_ = true;
      }
      
      // 发布累积点云
      // sensor_msgs::PointCloud2 output;
      // pcl::toROSMsg(*local_accumulated_cloud, output);
      // output.header.frame_id = frame_;
      // output.header.stamp = ros::Time::now();
      // cloud_pub_.publish(output);
    }
    
    rate.sleep();
  }
}

void LaserDetector::Runner() {
  double duration = option_.communicate_option.update_duration;

  // 检查合法性，避免除零或负数
  if (duration <= 0) {
    ROS_ERROR("Invalid duration %.2f seconds, using default 1.0 seconds.", duration);
    duration = 1.0; // 默认1秒
  }

  // 如果检测满/半笼，则需要调整频率  
  if (!stock_option_.empty()) {
    if (stock_option_.front().detect_fill_level) {
      duration = 2.5;
    }
  }

  // 根据持续时间计算频率（Hz = 1.0 / duration）
  ros::Rate rate(1.0 / duration);
  while (ros::ok() && !shutdown_flag_) {
    // 在HA模式下，如果是备机则直接休眠，不做任何处理
    if (ha_mode_enabled_ && !is_master_) {
      // 备机模式：不处理任何数据，只等待切换
      // ROS_DEBUG_STREAM(frame_ << " backup mode: standby");
      rate.sleep();
      continue;
    }
    
    // ROS_INFO_STREAM(frame_ << " running. ");
    if (!detect_option_.calibration) {
      // Skip stock detection for devices with no stocks
      if (!stock_option_.empty()) {
        if (Process()) {
          auto res = Detect();
          UpdateStocks(res);
          // pakage and send info
          // CycleSendStorageInfo();
        }
      } else {
        // For devices with no stocks, just process the point cloud to update alive status
        Process();
      }
    } else {
      // 标定功能不受HA模式影响
      CalLivoxInstallParams();
      Process();
    }
    rate.sleep();
  }
}

bool LaserDetector::Process() {
  // 安全地获取累积点云的引用
  {
    std::lock_guard<std::mutex> lock(accumulated_cloud_mutex_);
    if (!accumulated_cloud_updated_ || !accumulated_cloud_ || accumulated_cloud_->empty()) {
      return false;
    }
    
    // 直接使用智能指针共享点云数据，避免深拷贝
    detection_cloud_ = accumulated_cloud_;
    accumulated_cloud_updated_ = false;
  }
  
  return true;
}

std::map<std::string, StockState> LaserDetector::Detect() {
  std::map<std::string, StockState> res;
  
  // 检查是否有有效的点云用于检测
  if (!detection_cloud_ || detection_cloud_->empty()) {
    ROS_WARN("No valid point cloud for detection");
    return res;
  }

  for (const auto& stock : stock_option_) {
    // 使用预计算的库位边界坐标
    auto boundaries_it = stock_boundaries_cache_.find(stock.stock_id);
    auto z_ranges_it = stock_z_ranges_cache_.find(stock.stock_id);
    
    if (boundaries_it == stock_boundaries_cache_.end() || z_ranges_it == stock_z_ranges_cache_.end()) {
      ROS_ERROR_STREAM("Stock boundaries not found for " << stock.stock_id);
      continue;
    }
    
    const auto& [stock_min_corner, stock_max_corner] = boundaries_it->second;
    const auto& [z_min, z_max] = z_ranges_it->second;

    // 使用高效过滤方法，避免完整点云拷贝
    pcl::PointCloud<pcl::PointXYZ>::Ptr temp_cloud = RoiFilterCloudEfficient(
        detection_cloud_,
        z_min, z_max,
        stock_min_corner.x, stock_max_corner.x,
        stock_min_corner.y, stock_max_corner.y);

    // 如果点云数量小于全局配置的阈值，判断为空
    if (temp_cloud->size() < static_cast<size_t>(option_.point_cloud_threshold)) {
      res[stock.stock_id] = StockState::FREE;
      updateStockHeightScore(stock.stock_id, 0.0f);
      ROS_DEBUG_STREAM("Stock " << stock.stock_id << " marked as FREE: point count " 
                      << temp_cloud->size() << " < threshold " << option_.point_cloud_threshold);
      continue;
    }

    // 点云数量足够，根据是否需要检测满/半笼来判断状态
    if (stock.detect_fill_level) {
      // 需要判断满/半笼，分析高度分布
      // 提取高度信息
      std::vector<float> heights;
      heights.reserve(temp_cloud->size());
      for (const auto& point : temp_cloud->points) {
        heights.push_back(point.z);
      }
      
      // 对高度进行排序，用于计算统计指标
      std::sort(heights.begin(), heights.end());
      
      if (heights.size() < 4) {
        // 处理数据量不足的情况
        res[stock.stock_id] = StockState::HALF_FULL;
        updateStockHeightScore(stock.stock_id, 0.0f);
        continue;
      }
      
      // 计算多种统计指标
      size_t median_idx = std::max(0UL, std::min(heights.size() - 1, heights.size() / 2));
      float median_height = heights[median_idx];
      float q3_height = heights[heights.size() * 3 / 4];
      
      // 计算平均高度
      float mean_height = std::accumulate(heights.begin(), heights.end(), 0.0f) / heights.size();
      
      // 计算最高10%点的平均高度
      size_t top_start_idx = heights.size() * 0.9;
      float top_mean_height = std::accumulate(heights.begin() + top_start_idx, heights.end(), 0.0f) 
                             / (heights.size() - top_start_idx);
      
      // 计算加权高度得分
      float height_score = 0.5 * median_height + 0.1 * q3_height + 0.3 * mean_height + 0.1 * top_mean_height;
      
      ROS_INFO_STREAM("Stock " << stock.stock_id << " height stats: median=" << median_height 
                    << ", q3=" << q3_height << ", mean=" << mean_height 
                    << ", top10%_mean=" << top_mean_height);
      ROS_INFO_STREAM("Stock " << stock.stock_id << " height score: " << height_score);
      
      // 根据高度得分判断状态
      const double full_threshold = detect_option_.full_height_threshold;
      if (height_score > full_threshold) {
        res[stock.stock_id] = StockState::OCCUPY;  // 满笼
        ROS_INFO_STREAM("detect result for:" << stock.stock_id << ": occupy (full)");
      } else {
        res[stock.stock_id] = StockState::HALF_FULL;  // 半笼
        ROS_INFO_STREAM("detect result for:" << stock.stock_id << ": half_full");
      }
      
      // 保存height_score
      updateStockHeightScore(stock.stock_id, height_score);
    } else {
      // 不需要检测满/半笼，直接标记为占据
      res[stock.stock_id] = StockState::OCCUPY;
      updateStockHeightScore(stock.stock_id, 0.0f);
      ROS_DEBUG_STREAM("Stock " << stock.stock_id << " marked as OCCUPY: point count " 
                      << temp_cloud->size() << " >= threshold " << option_.point_cloud_threshold);
    }
  }

  return res;
}

void LaserDetector::UpdateStocks(std::map<std::string, StockState> detectResult) {
  auto func = [&](const std::shared_ptr<Stock>& stock, const StockState& state) {
    stock->updateStockState(state);
    // height_score already updated in Detect method
  };

  for (auto& stockRes : detectResult) {
    thread_pool_.enqueue(func, id_stocks_[stockRes.first], stockRes.second);
  }
}

bool LaserDetector::CalibrateRequest(cotek_laser_detect::calibrate::Request& req,
                                      cotek_laser_detect::calibrate::Response& res) {
  request_calibrate_ = true;
  
  if (req.points_a.size() != req.points_b.size()) {
    ROS_ERROR("Point pairs size mismatch!");
    res.success = false;
    return true;
  }

  correspondence_points_.clear();
  for (size_t i = 0; i < req.points_a.size(); ++i) {
      geometry_msgs::Point lidar_point = req.points_a[i];
      geometry_msgs::Point world_point = req.points_b[i];
      ROS_INFO("Pair %zu: A(%f, %f, %f) -> B(%f, %f, %f)",
                i, lidar_point.x, lidar_point.y, lidar_point.z, world_point.x, world_point.y, world_point.z);
      // 添加对应点
      AddCorrespondencePoint(pcl::PointXYZ(lidar_point.x, lidar_point.y, lidar_point.z), 
                              pcl::PointXYZ(world_point.x, world_point.y, world_point.z));

  }

  res.success = true;

  return true;
}


void LaserDetector::CalLivoxInstallParams() {
    if (!request_calibrate_) return;
    if (!vaild_cloud_.vaild) return;

    pcl::PointCloud<pcl::PointXYZ> cloud;
    {
        std::unique_lock<std::mutex> lock(mutex_);
        cloud = vaild_cloud_.cloud;
        vaild_cloud_.vaild = false;
    }

    if (get_frame_num_ == 0) all_cloud_.clear();

    get_frame_num_++;
    ROS_WARN("[Calibration] Collected frame: %d/15", get_frame_num_);
    
    if (get_frame_num_ < 15) {
        all_cloud_ += cloud;
        return;
    } else {
        get_frame_num_ = 0;
    }

    pcl::PointCloud<pcl::PointXYZ>::Ptr final_cloud_ptr(new pcl::PointCloud<pcl::PointXYZ>);
    pcl::copyPointCloud(all_cloud_, *final_cloud_ptr);
    ROS_WARN("----- Start Calibration -----");
    
    // ROI滤波（确保选择地面区域）
    RoiFilterCloud(final_cloud_ptr, 
                   detect_option_.origin_roi_filter.z_min,
                   detect_option_.origin_roi_filter.z_max,
                   detect_option_.origin_roi_filter.x_min,
                   detect_option_.origin_roi_filter.x_max,
                   detect_option_.origin_roi_filter.y_min,
                   detect_option_.origin_roi_filter.y_max,
                   false);

    if (final_cloud_ptr->empty()) {
        ROS_ERROR("Final cloud is empty after ROI filter");
        request_calibrate_ = false;
        return;
    }

    // 添加标定对应点约束
    if (correspondence_points_.size() < 3){
      ROS_WARN("transform points pair is less than 3 !!!!!!!");
      request_calibrate_ = false;

      if (pcl::io::savePCDFile(saved_pcd_path_, *final_cloud_ptr) == 0){
          ROS_INFO_STREAM("Saved point cloud to " << saved_pcd_path_);
      }else{
        ROS_ERROR_STREAM("Error saving point cloud to " << saved_pcd_path_);
      }
      
      return;
    }

    // 使用Calibration类进行标定
    Calibration calibrator(frame_);
    
    // 调用calibration方法，传入点云和对应点
    bool calibration_success = calibrator.calibrate(final_cloud_ptr, correspondence_points_);
    
    if (!calibration_success) {
        ROS_ERROR("Calibration failed");
        request_calibrate_ = false;
        return;
    }
    
    // 获取标定结果
    calib_params_ = calibrator.getCalibrationParams();

    // 写入到配置文件
    saveCalibrationParams(frame_, calib_params_, tf_path_);

    // 清空校准点
    correspondence_points_.clear();

    // 输出结果
    ROS_WARN_STREAM("Calibration Result:\n"
    << "Roll: " << calib_params_.roll << " rad\n"
    << "Pitch: " << calib_params_.pitch << " rad\n"
    << "Yaw: " << calib_params_.yaw << " rad\n"
    << "Translation: [" << calib_params_.tx << ", "
    << calib_params_.ty << ", " << calib_params_.tz << "]");

    request_calibrate_ = false;
}

// Add a new method to check if lidar is alive
bool LaserDetector::IsAlive() const {
  // 在HA模式下，如果是备机，则激光器始终为不存活状态
  if (ha_mode_enabled_ && !is_master_) {
    return false;
  }
  
  ros::Time current_time = ros::Time::now();
  double timeout = option_.communicate_option.update_duration * 2; // Double the update duration as timeout
  
  // If no timeout defined or invalid, use a default 2-second timeout
  if (timeout <= 0) {
    timeout = 2.0;
  }
  
  // Check if we've received data within the timeout period
  return (current_time - last_data_received_).toSec() < timeout;
}

// 新增的辅助方法，用于更新库位高度得分
void LaserDetector::updateStockHeightScore(const std::string& stock_id, float height_score) {
  auto it = id_stocks_.find(stock_id);
  if (it != id_stocks_.end()) {
    it->second->updateHeightScore(height_score);
  }
}

bool LaserDetector::SaveDetectionCloudRequest(cotek_laser_detect::save_detection_cloud::Request& req,
                                              cotek_laser_detect::save_detection_cloud::Response& res) {
  try {
    // 检查是否有有效的检测点云
    if (!detection_cloud_ || detection_cloud_->empty()) {
      res.success = false;
      res.message = "No valid detection cloud available";
      ROS_WARN_STREAM(frame_ << ": " << res.message);
      return true;
    }

    // 生成文件名
    std::string filename;
    if (req.filename.empty()) {
      // 使用时间戳作为文件名
      auto now = std::chrono::system_clock::now();
      auto time_t = std::chrono::system_clock::to_time_t(now);
      auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
          now.time_since_epoch()) % 1000;
      
      std::stringstream ss;
      ss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
      ss << "_" << std::setfill('0') << std::setw(3) << ms.count();
      filename = frame_ + "_detection_" + ss.str();
    } else {
      filename = req.filename;
    }

    // 构建完整路径
    std::string save_dir = option_.package_path + "/config/detection_clouds/";
    
    // 创建目录（如果不存在）
    std::string mkdir_cmd = "mkdir -p " + save_dir;
    if (system(mkdir_cmd.c_str()) != 0) {
      ROS_WARN_STREAM("Failed to create directory: " << save_dir);
    }
    
    std::string full_path = save_dir + filename + ".pcd";

    // 保存点云
    if (pcl::io::savePCDFileBinary(full_path, *detection_cloud_) == 0) {
      res.success = true;
      res.message = "Detection cloud saved successfully";
      res.saved_path = full_path;
      ROS_INFO_STREAM(frame_ << ": Detection cloud saved to " << full_path 
                      << " (points: " << detection_cloud_->size() << ")");
    } else {
      res.success = false;
      res.message = "Failed to save detection cloud to file";
      ROS_ERROR_STREAM(frame_ << ": " << res.message << " - " << full_path);
    }

  } catch (const std::exception& e) {
    res.success = false;
    res.message = std::string("Exception occurred: ") + e.what();
    ROS_ERROR_STREAM(frame_ << ": " << res.message);
  }

  return true;
}

}

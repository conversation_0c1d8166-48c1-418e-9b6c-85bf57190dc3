/**
 * Copyright (c) 2022 CoTEK Inc. All rights reserved.
 */

#include "stock.h"

namespace cotek_laser_detect {

#define RECORD 0

static const std::string GetTimeStampNow() {
  auto stamp =
      std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
  tm *tt = localtime(&stamp);
  std::string name_timed =
      std::to_string(tt->tm_year + 1900) + "_" +
      std::to_string(tt->tm_mon + 1) + "_" + std::to_string(tt->tm_mday) + "_" +
      std::to_string(tt->tm_hour) + "_" + std::to_string(tt->tm_min) + "_" +
      std::to_string(tt->tm_sec);
  // std::cout << "name_timed : " << name_timed << std::endl;
  return name_timed;
}

void StockStateQueue::PushData(const StockState &state) {
  std::unique_lock<std::mutex> lock(mutex_);
  if (this->size() >= filter_num_) pop_front();
  push_back(state);
}

StockState StockStateQueue::GetData() {
  std::unique_lock<std::mutex> lock(mutex_);
  if (this->size() < filter_num_) return StockState::NONE;

  // 如果是半满笼检测（取货位），则状态都是满才认为是满
  if (detect_fill_level_) {
    // 有一次为空，则认为为空
    for (const auto &state : *this) {
      if (state == StockState::NONE || state == StockState::FREE) return state;
    }

    // 如果不为空，取最后一个状态
    return this->back();
  }

  // 如果是卸货位，直接取最后一个状态
  return this->back();

  // // 仅数据均是FREE 才判断为FREE
  // for (const auto &state : *this) {
  //   if (state == StockState::NONE) return StockState ::NONE;
  // }

  // for (const auto &state : *this) {
  //   if (state == StockState::OCCUPY) return StockState ::OCCUPY;
  // }

  // for (const auto &state : *this) {
  //   if (state == StockState::HALF_FULL) return StockState ::HALF_FULL;
  // } 

  // return StockState ::FREE;
}

Stock::Stock(const StockOption &option)
    : option_(option),
      state_queue_(2, option.detect_fill_level),
      last_save_time_(std::chrono::steady_clock::now()) {}

void Stock::updateStockState(StockState state){
  std::lock_guard<std::mutex> lock(mutex_);
  state_queue_.PushData(state);
}

void Stock::updateHeightScore(float score) {
  std::lock_guard<std::mutex> lock(mutex_);
  height_score_ = score;
}

}  // namespace cotek_cam_detect

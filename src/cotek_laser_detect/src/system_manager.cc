#include "system_manager.h"
#include <iostream>
#include <pwd.h>
#include <ros/package.h>

SystemManager::SystemManager() : is_initialized_(false), is_shutting_down_(false) {}

SystemManager::~SystemManager() {
  Shutdown();
}

bool SystemManager::Initialize(const std::string& package_path) {
  std::lock_guard<std::mutex> lock(mutex_);
  
  if (is_initialized_) {
    ROS_WARN("System already initialized, cleaning up first...");
    CleanupAll();
  }
  
  ROS_INFO("Initializing system...");
  
  // 设置配置文件路径
  package_path_ = package_path;
  communication_path_ = package_path_ + "/config/communication.json";
  storage_path_ = package_path_ + "/config/storage.json";
  tf_path_ = package_path_ + "/config/tf.json";
  detect_path_ = package_path_ + "/config/detect.json";
  
  option_.package_path = package_path_;
  
  // 加载所有配置
  if (!LoadAllConfigs()) {
    ROS_ERROR("Failed to load configurations");
    return false;
  }
  
  // 创建NodeHandle
  nh_ = std::make_unique<ros::NodeHandle>();
  ros::Duration(0.5).sleep(); // 等待ROS日志系统就绪
  
  // 初始化检测器
  if (!InitializeDetectors()) {
    ROS_ERROR("Failed to initialize detectors");
    return false;
  }
  
  ros::Duration(2.5).sleep();
  
  // 初始化状态报告器
  if (!InitializeStatusReporter()) {
    ROS_ERROR("Failed to initialize status reporter");
    return false;
  }
  
  // 初始化HA管理器
  if (!InitializeHAManager()) {
    ROS_ERROR("Failed to initialize HA manager");
    return false;
  }
  
  // 初始化定时器
  if (!InitializeTimers()) {
    ROS_ERROR("Failed to initialize timers");
    return false;
  }
  
  is_initialized_ = true;
  ROS_INFO("System initialized successfully");
  return true;
}

bool SystemManager::Restart() {
  std::lock_guard<std::mutex> lock(mutex_);
  
  ROS_INFO("Restarting system...");
  
  // 先清理所有对象
  CleanupAll();
  
  // 重新加载配置（不重新设置路径，因为这些在Initialize时已经设置）
  if (!LoadAllConfigs()) {
    ROS_ERROR("Failed to reload configurations during restart");
    return false;
  }
  
  // 重新初始化所有对象
  // 创建新的NodeHandle
  nh_ = std::make_unique<ros::NodeHandle>();
  ros::Duration(0.5).sleep();
  
  if (!InitializeDetectors()) {
    ROS_ERROR("Failed to reinitialize detectors during restart");
    return false;
  }
  
  ros::Duration(2.5).sleep();
  
  if (!InitializeStatusReporter()) {
    ROS_ERROR("Failed to reinitialize status reporter during restart");
    return false;
  }
  
  if (!InitializeHAManager()) {
    ROS_ERROR("Failed to reinitialize HA manager during restart");
    return false;
  }
  
  if (!InitializeTimers()) {
    ROS_ERROR("Failed to reinitialize timers during restart");
    return false;
  }
  
  is_initialized_ = true;
  ROS_INFO("System restarted successfully");
  return true;
}

void SystemManager::Shutdown() {
  std::lock_guard<std::mutex> lock(mutex_);
  
  if (is_shutting_down_) {
    return; // 避免重复关闭
  }
  
  is_shutting_down_ = true;
  ROS_INFO("Shutting down system...");
  
  CleanupAll();
  ROS_INFO("System shutdown complete");
}

bool SystemManager::LoadAllConfigs() {
  ROS_INFO_STREAM("Loading configurations from: " << communication_path_);
  
  if (!LoadCommunicationConfig(communication_path_, &option_)) {
    ROS_ERROR("Communication config is error!!!");
    return false;
  }
  
  if (!LoadTfConfig(tf_path_, &option_)) {
    ROS_ERROR("TF config is error!!!");
    return false;
  }
  
  if (!LoadDetectConfig(detect_path_, &option_)) {
    ROS_ERROR("Detect config is error!!!");
    return false;
  }
  
  if (!LoadStorageConfig(storage_path_, &option_)) {
    ROS_ERROR("Storage config is error!!!");
    return false;
  }
  
  return true;
}

bool SystemManager::InitializeDetectors() {
  detect_ptr_map_.clear();
  
  // Initialize detectors for all devices in detect_option
  for (const auto &device_entry : option_.detect_option) {
    std::string laser_frame = device_entry.first;
    ROS_INFO_STREAM("Initializing detector for device: " << laser_frame);
    
    auto detect_ptr = std::make_shared<cotek_laser_detect::LaserDetector>(option_, laser_frame);
    bool res = detect_ptr->Init();
    detect_ptr_map_[laser_frame] = detect_ptr;
    
    if (res) {
      ROS_INFO_STREAM("Successfully initialized detector for device: " << laser_frame);
    } else {
      ROS_WARN_STREAM("Failed to initialize detector for device: " << laser_frame);
    }
  }
  
  return true;
}

bool SystemManager::InitializeStatusReporter() {
  status_reporter_ = std::make_unique<cotek_laser_detect::StatusReporter>(option_);
  return true;
}

bool SystemManager::InitializeHAManager() {
  ha_enabled_ = option_.communicate_option.enable_ha;
  
  if (ha_enabled_) {
    ha_manager_ = std::make_unique<cotek_laser_detect::HAManager>(
        option_.communicate_option.vip_address, 
        option_.communicate_option.ha_interface);
    
    bool is_master = ha_manager_->IsMaster();
    last_master_status_ = is_master;
    ROS_INFO_STREAM("HA Mode enabled. Current status: " << ha_manager_->GetStatusString() << 
                    " (VIP: " << option_.communicate_option.vip_address << ")");
    
    // 为所有检测器设置HA模式
    for (auto& detector_pair : detect_ptr_map_) {
      detector_pair.second->SetHAMode(true, is_master);
    }
  } else {
    ROS_INFO_STREAM("HA Mode disabled. All functions will run normally.");
    // 为所有检测器设置非HA模式
    for (auto& detector_pair : detect_ptr_map_) {
      detector_pair.second->SetHAMode(false, true);
    }
  }
  
  return true;
}

bool SystemManager::InitializeTimers() {
  // 停止现有定时器
  stock_timer_.stop();
  laser_alive_timer_.stop();
  ha_status_timer_.stop();
  
  // 创建库存状态上报定时器
  stock_timer_ = nh_->createTimer(
      ros::Duration(option_.communicate_option.duration),
      [this](const ros::TimerEvent &) {
        try {
          if (!ha_enabled_ || (ha_manager_ && ha_manager_->IsMaster())) {
            // 暂时注释掉库位状态主动上报 - 可通过 /api/storage-status 查询
            status_reporter_->reportAllStockStatus(detect_ptr_map_);
          }
        } catch (const std::exception& e) {
          ROS_ERROR_STREAM("Stock status report failed: " << e.what());
        } catch (...) {
          ROS_ERROR("Unknown exception in stock status report");
        }
      });
  
  // 创建激光器存活状态上报定时器
  double laser_alive_check_interval = 5.0;
  if (option_.communicate_option.laser_alive_check_interval > 0) {
    laser_alive_check_interval = option_.communicate_option.laser_alive_check_interval;
  }
  
  laser_alive_timer_ = nh_->createTimer(
      ros::Duration(laser_alive_check_interval),
      [this](const ros::TimerEvent &) {
        try {
          if (!ha_enabled_ || (ha_manager_ && ha_manager_->IsMaster())) {
            status_reporter_->reportLaserAliveStatus(detect_ptr_map_);
          }
        } catch (const std::exception& e) {
          ROS_ERROR_STREAM("Laser alive status report failed: " << e.what());
        } catch (...) {
          ROS_ERROR("Unknown exception in laser alive status report");
        }
      });
  
  // 如果启用了HA，创建HA状态检查定时器
  if (ha_enabled_ && ha_manager_) {
    double ha_check_interval = option_.communicate_option.ha_check_interval;
    
    ha_status_timer_ = nh_->createTimer(
        ros::Duration(ha_check_interval),
        [this](const ros::TimerEvent &) {
          bool current_master_status = ha_manager_->IsMaster();
          if (current_master_status != last_master_status_) {
            ROS_WARN_STREAM("HA Status changed: " << 
                           (last_master_status_ ? "master" : "backup") << " -> " << 
                           (current_master_status ? "master" : "backup"));
            
            // 更新所有检测器的HA状态
            for (auto& detector_pair : detect_ptr_map_) {
              detector_pair.second->UpdateHAStatus(current_master_status);
            }
            
            last_master_status_ = current_master_status;
          }
        });
  }
  
  return true;
}

void SystemManager::CleanupAll() {
  is_initialized_ = false;
  
  // 停止定时器
  stock_timer_.stop();
  laser_alive_timer_.stop();
  ha_status_timer_.stop();
  
  // 清理对象
  detect_ptr_map_.clear();
  status_reporter_.reset();
  ha_manager_.reset();
  nh_.reset();
  
  ROS_INFO("All objects cleaned up");
} 
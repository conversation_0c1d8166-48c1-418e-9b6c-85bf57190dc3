#include <ros/ros.h>
#include <iostream>
#include <pwd.h>
#include <ros/package.h>
#include <thread>
#include <mutex>
#include <atomic>

#include "config.h"
#include "httplib.h"
#include "system_manager.h"

#define KErrorExitCode -1

// 全局系统管理器实例
std::unique_ptr<SystemManager> g_system_manager;

// HTTP接口处理函数
void handleReloadConfig(const httplib::Request& req, httplib::Response& res) {
  ROS_INFO("Received config reload request via HTTP");
  
  if (!g_system_manager) {
    res.status = 500;
    res.set_content("{\"success\": false, \"message\": \"System manager not initialized\"}", "application/json");
    return;
  }
  
  // 先获取当前配置以便进行单次API尝试
  LaserStorageOption temp_option;
  
  // 获取当前通信配置
  std::string package_path = "/sky-watch";
  std::string communication_path = package_path + "/config/communication.json";
  std::string storage_path = package_path + "/config/storage.json";
  
  if (!LoadCommunicationConfig(communication_path, &temp_option)) {
    ROS_ERROR("Failed to load communication config for API reload");
    res.status = 500;
    res.set_content("{\"success\": false, \"message\": \"Failed to load communication config\"}", "application/json");
    return;
  }
  
  // 尝试从API加载配置（单次尝试）
  if (!LoadStorageConfigFromAPISingleAttempt(&temp_option, storage_path)) {
    ROS_ERROR("Failed to reload storage config from RCS");
    res.status = 500;
    res.set_content("{\"success\": false, \"message\": \"Failed to reload config from RCS\"}", "application/json");
    return;
  }
  
  // 配置加载成功，重启系统
  if (!g_system_manager->Restart()) {
    ROS_ERROR("Failed to restart system after config reload");
    res.status = 500;
    res.set_content("{\"success\": false, \"message\": \"Config reloaded but system restart failed\"}", "application/json");
    return;
  }
  
  ROS_INFO("Config reload and system restart completed successfully");
  res.status = 200;
  res.set_content("{\"success\": true, \"message\": \"Config reloaded and system restarted successfully\"}", "application/json");
}

// HTTP接口处理函数 - 查询库位状态
void handleStorageStatus(const httplib::Request& req, httplib::Response& res) {
  ROS_INFO("Received storage status query request via HTTP");
  
  if (!g_system_manager) {
    res.status = 500;
    res.set_content("{\"success\": false, \"message\": \"System manager not initialized\"}", "application/json");
    return;
  }
  
  if (!g_system_manager->IsInitialized()) {
    res.status = 503;
    res.set_content("{\"success\": false, \"message\": \"System not initialized\"}", "application/json");
    return;
  }
  
  try {
    // 获取所有检测器的状态
    auto detectors = g_system_manager->GetDetectors();
    
    // 收集所有激光器的库位状态
    std::map<std::string, std::shared_ptr<cotek_laser_detect::Stock>> all_id_stocks;
    for (const auto &detector_pair : detectors) {
      const auto &detector = detector_pair.second;
      auto id_stocks = detector->GetCurrentStatus();
      all_id_stocks.insert(id_stocks.begin(), id_stocks.end());
    }
    
    // 获取状态报告器并生成状态信息
    auto status_reporter = g_system_manager->GetStatusReporter();
    if (!status_reporter) {
      res.status = 500;
      res.set_content("{\"success\": false, \"message\": \"Status reporter not available\"}", "application/json");
      return;
    }
    
    // 使用状态报告器打包库位信息
    std::string storage_info = status_reporter->PackStorageInfo(all_id_stocks);
    
    ROS_INFO("Storage status query completed successfully");
    res.status = 200;
    res.set_content(storage_info, "application/json");
    
  } catch (const std::exception& e) {
    ROS_ERROR_STREAM("Storage status query failed: " << e.what());
    res.status = 500;
    res.set_content("{\"success\": false, \"message\": \"Internal server error\"}", "application/json");
  } catch (...) {
    ROS_ERROR("Unknown exception in storage status query");
    res.status = 500;
    res.set_content("{\"success\": false, \"message\": \"Unknown error\"}", "application/json");
  }
}

int main(int argc, char **argv) {
  ros::init(argc, argv, "cotek_laser_detect");
  
  // Set ros log level
  if (ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Info)) {
    ros::console::notifyLoggerLevelsChanged();
  }
  
  // 设置路径
  std::string package_path = "/sky-watch";
  // std::string package_path = ros::package::getPath("cotek_laser_detect"); // 本地调试用
  
  // 创建系统管理器
  g_system_manager = std::make_unique<SystemManager>();
  
  // 初始化系统
  if (!g_system_manager->Initialize(package_path)) {
    ROS_ERROR("Failed to initialize system");
    return KErrorExitCode;
  }
  
  // 创建HTTP服务器
  httplib::Server http_server;
  
  // 注册配置重新加载接口
  http_server.Post("/api/reload-config", handleReloadConfig);
  
  // 添加健康检查接口
  http_server.Get("/api/health", [](const httplib::Request& req, httplib::Response& res) {
    if (g_system_manager && g_system_manager->IsInitialized()) {
      res.status = 200;
      res.set_content("{\"status\": \"healthy\", \"initialized\": true}", "application/json");
    } else {
      res.status = 503;
      res.set_content("{\"status\": \"unhealthy\", \"initialized\": false}", "application/json");
    }
  });
  
  // 添加查询库位状态接口
  // http_server.Get("/api/storage-status", handleStorageStatus);
  
  // 在后台线程中启动HTTP服务器
  std::thread http_thread([&http_server]() {
    int http_port = 8080; // 可以从配置文件中读取
    ROS_INFO_STREAM("Starting HTTP server on port " << http_port);
    if (!http_server.listen("0.0.0.0", http_port)) {
      ROS_ERROR_STREAM("Failed to start HTTP server on port " << http_port);
    }
  });
  
  ROS_INFO("System started successfully. HTTP API available on port 8080");
  ROS_INFO("Available endpoints:");
  ROS_INFO("  POST /api/reload-config - Reload configuration from API");
  ROS_INFO("  GET  /api/health        - Check system health");
  ROS_INFO("  GET  /api/storage-status - Check storage status");
  
  // 运行ROS主循环
  ros::spin();
  
  // 清理资源
  ROS_INFO("Shutting down...");
  http_server.stop();
  if (http_thread.joinable()) {
    http_thread.join();
  }
  
  g_system_manager.reset();
  
  return 0;
}
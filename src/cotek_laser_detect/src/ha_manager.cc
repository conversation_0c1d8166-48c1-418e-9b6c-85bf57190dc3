#include "ha_manager.h"
#include <cstdlib>
#include <fstream>
#include <sstream>
#include <arpa/inet.h>
#include <ifaddrs.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <cstring>

namespace cotek_laser_detect {

HAManager::HAManager(const std::string& vip_address, const std::string& interface)
    : vip_address_(vip_address), interface_(interface) {
}

HAManager::~HAManager() {
}

bool HAManager::IsMaster() const {
    if (vip_address_.empty()) {
        ROS_WARN("VIP address not set, assuming backup mode");
        return false;
    }
    
    return CheckVIPAssigned();
}

void HAManager::SetVIPAddress(const std::string& vip_address) {
    vip_address_ = vip_address;
}

void HAManager::SetInterface(const std::string& interface) {
    interface_ = interface;
}

std::string HAManager::GetStatusString() const {
    return IsMaster() ? "master" : "backup";
}

bool HAManager::CheckVIPAssigned() const {
    if (interface_.empty()) {
        // Check all interfaces
        struct ifaddrs *ifaddrs_ptr = nullptr;
        if (getifaddrs(&ifaddrs_ptr) == -1) {
            ROS_ERROR("Failed to get network interfaces");
            return false;
        }
        
        bool found = false;
        for (struct ifaddrs *ifa = ifaddrs_ptr; ifa != nullptr; ifa = ifa->ifa_next) {
            if (ifa->ifa_addr == nullptr) continue;
            
            if (ifa->ifa_addr->sa_family == AF_INET) {
                struct sockaddr_in* sa_in = (struct sockaddr_in*)ifa->ifa_addr;
                char ip_str[INET_ADDRSTRLEN];
                if (inet_ntop(AF_INET, &sa_in->sin_addr, ip_str, INET_ADDRSTRLEN) != nullptr) {
                    if (vip_address_ == std::string(ip_str)) {
                        found = true;
                        ROS_INFO("Found VIP %s on interface %s", vip_address_.c_str(), ifa->ifa_name);
                        break;
                    }
                }
            }
        }
        
        freeifaddrs(ifaddrs_ptr);
        return found;
    } else {
        // Check specific interface
        return CheckVIPOnInterface(interface_);
    }
}

bool HAManager::CheckVIPOnInterface(const std::string& interface_name) const {
    struct ifaddrs *ifaddrs_ptr = nullptr;
    if (getifaddrs(&ifaddrs_ptr) == -1) {
        ROS_ERROR("Failed to get network interfaces");
        return false;
    }
    
    bool found = false;
    for (struct ifaddrs *ifa = ifaddrs_ptr; ifa != nullptr; ifa = ifa->ifa_next) {
        if (ifa->ifa_addr == nullptr) continue;
        
        // Check if this is the interface we're looking for
        if (std::string(ifa->ifa_name) == interface_name && ifa->ifa_addr->sa_family == AF_INET) {
            struct sockaddr_in* sa_in = (struct sockaddr_in*)ifa->ifa_addr;
            char ip_str[INET_ADDRSTRLEN];
            if (inet_ntop(AF_INET, &sa_in->sin_addr, ip_str, INET_ADDRSTRLEN) != nullptr) {
                if (vip_address_ == std::string(ip_str)) {
                    found = true;
                    ROS_INFO("Found VIP %s on interface %s", vip_address_.c_str(), interface_name.c_str());
                    break;
                }
            }
        }
    }
    
    freeifaddrs(ifaddrs_ptr);
    return found;
}

} // namespace cotek_laser_detect 
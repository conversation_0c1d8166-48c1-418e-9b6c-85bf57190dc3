#include <ros/ros.h>
#include <pcl/io/pcd_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/segmentation/sac_segmentation.h>
#include <pcl/common/transforms.h>
#include <pcl/segmentation/region_growing.h>
#include <pcl/features/normal_3d.h>
#include <pcl/filters/extract_indices.h>
#include <Eigen/Core>
#include <Eigen/Geometry>
#include <iostream>
#include <string>
#include <vector>
#include <fstream>
#include <limits>
#include <yaml-cpp/yaml.h>
#include <json/json.h>
#include <boost/filesystem.hpp>
#include <termios.h>
#include <unistd.h>
#include "../include/cotek_enum.h"
#include "../include/calibration.h"

using namespace cotek_laser_detect;
namespace fs = boost::filesystem;

// Function to read a character without waiting for Enter
char getch() {
    char buf = 0;
    struct termios old = {0};
    if (tcgetattr(0, &old) < 0)
        perror("tcsetattr()");
    old.c_lflag &= ~ICANON;
    old.c_lflag &= ~ECHO;
    old.c_cc[VMIN] = 1;
    old.c_cc[VTIME] = 0;
    if (tcsetattr(0, TCSANOW, &old) < 0)
        perror("tcsetattr ICANON");
    if (read(0, &buf, 1) < 0)
        perror("read()");
    old.c_lflag |= ICANON;
    old.c_lflag |= ECHO;
    if (tcsetattr(0, TCSADRAIN, &old) < 0)
        perror("tcsetattr ~ICANON");
    return buf;
}

// Function to save calibration parameters to a JSON file (from laser_detect.cc)
void saveCalibrationParams(const std::string& frame, const cotek_laser_detect::CalibrationParams& params, 
                           const std::string& filename) {
    // Read or initialize JSON data
    std::string json_str;
    std::ifstream in_file(filename);
    if (in_file.good()) {
        in_file.seekg(0, std::ios::end);
        size_t size = in_file.tellg();
        in_file.seekg(0, std::ios::beg);
        json_str.resize(size);
        in_file.read(&json_str[0], size);
        in_file.close();
    } else {
        ROS_ERROR("tf.json path error");
        json_str = R"({"laser_tf": []})";
        return;
    }

    // Parse JSON
    Json::Value root;
    Json::CharReaderBuilder builder;
    builder["collectComments"] = false;
    std::string errs;
    std::istringstream iss(json_str);
    if (!Json::parseFromStream(builder, iss, &root, &errs)) {
        ROS_ERROR("Failed to parse JSON: %s", errs.c_str());
        return;
    }
    
    // Handle parse errors or invalid structure
    if (!root.isMember("laser_tf") || !root["laser_tf"].isArray()) {
        root["laser_tf"] = Json::Value(Json::arrayValue);
    }

    // Get laser_tf array
    Json::Value& laser_tf = root["laser_tf"];
    bool found = false;

    // Search for matching frame
    for (Json::Value& entry : laser_tf) {
        if (entry["frame"].asString() == frame) {
            // Create new tf object
            Json::Value tf_obj;
            tf_obj["x"] = params.tx;
            tf_obj["y"] = params.ty;
            tf_obj["z"] = params.tz;
            tf_obj["roll"] = params.roll;
            tf_obj["pitch"] = params.pitch;
            tf_obj["yaw"] = params.yaw;
            
            // Update entry
            entry["tf"] = tf_obj;
            found = true;
            break;
        }
    }

    // Add new entry if not found
    if (!found) {
        Json::Value new_entry;
        new_entry["frame"] = frame;
        
        Json::Value tf_obj;
        tf_obj["x"] = params.tx;
        tf_obj["y"] = params.ty;
        tf_obj["z"] = params.tz;
        tf_obj["roll"] = params.roll;
        tf_obj["pitch"] = params.pitch;
        tf_obj["yaw"] = params.yaw;
        
        new_entry["tf"] = tf_obj;
        laser_tf.append(new_entry);
    }

    // Write to file
    std::ofstream out_file(filename);
    if (out_file.is_open()) {
        try {
            Json::StreamWriterBuilder builder;
            builder["indentation"] = "  ";  // Set indentation to 2 spaces
            std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
            writer->write(root, &out_file);
            out_file.close();
            
            if (out_file.fail()) {
                ROS_ERROR_STREAM("Write operation failed for file: " << filename << " (disk full?)");
            } else {
                ROS_WARN_STREAM("Successfully saved parameters to " << filename);
            }
        } catch (const std::exception& e) {
            ROS_ERROR_STREAM("Exception during file write: " << e.what());
        }
    } else {
        ROS_ERROR_STREAM("Failed to open file " << filename << " for writing");
        ROS_WARN_STREAM("Calibration parameters NOT saved for frame: " << frame);
    }
}

class CalibrationTool {
public:
    CalibrationTool(const std::string& frame_name) : frame_name_(frame_name), calibration_(frame_name) {
        cloud.reset(new pcl::PointCloud<pcl::PointXYZ>());
        filtered_cloud.reset(new pcl::PointCloud<pcl::PointXYZ>());
        correspondence_points.clear();
        
        ROS_INFO("Calibration Tool initialized");
    }
    
    bool loadPCD(const std::string& file_path) {
        if (pcl::io::loadPCDFile<pcl::PointXYZ>(file_path, *cloud) == -1) {
            ROS_ERROR("Couldn't read file %s", file_path.c_str());
            return false;
        }
        ROS_INFO("Loaded %zu points from %s", cloud->size(), file_path.c_str());
        
        // Since PCD file is already filtered, just copy to filtered_cloud
        filtered_cloud.reset(new pcl::PointCloud<pcl::PointXYZ>());
        pcl::copyPointCloud(*cloud, *filtered_cloud);
        ROS_INFO("Using loaded cloud directly without filtering");
        
        return true;
    }
    
    void addCorrespondencePoint(const pcl::PointXYZ& lidar_p, const pcl::PointXYZ& world_p) {
        CorrespondencePoint cp;
        cp.lidar_point = lidar_p;
        cp.world_point = world_p;
        correspondence_points.push_back(cp);
        
        ROS_INFO("Added correspondence point %zu: Lidar(%f, %f, %f) -> World(%f, %f, %f)",
                correspondence_points.size(), 
                lidar_p.x, lidar_p.y, lidar_p.z,
                world_p.x, world_p.y, world_p.z);
    }

    bool calibrate() {
        // Use the Calibration class to perform the calibration
        // bool result = calibration_.calibrate(filtered_cloud, correspondence_points, true);
        bool result = calibration_.calibrate(filtered_cloud, correspondence_points, false);
        
        if (result) {
            // Get the calibration parameters
            calib_params = calibration_.getCalibrationParams();
        }
        
        return result;
    }
    
    bool loadCorrespondencePointsFromYaml(const std::string& yaml_file, const std::string& frame_name) {
        return calibration_.loadCorrespondencePointsFromYaml(yaml_file, frame_name, correspondence_points);
    }
    
    // Getter methods to access calibration parameters
    CalibrationParams getCalibrationParams() const {
        return calib_params;
    }
    
private:
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud;
    pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud;
    std::vector<CorrespondencePoint> correspondence_points;
    CalibrationParams calib_params;
    std::string frame_name_;
    Calibration calibration_;
};

void printUsage() {
    std::cout << "Usage: calibration_tool <pcd_dir> <yaml_dir> <tf_json_path> [frame_name]" << std::endl;
    std::cout << "  pcd_dir: Directory containing PCD files" << std::endl;
    std::cout << "  yaml_dir: Directory containing YAML files with correspondence points" << std::endl;
    std::cout << "  tf_json_path: Path to tf.json file to save calibration parameters" << std::endl;
    std::cout << "  frame_name: (Optional) Specific frame name to calibrate" << std::endl;
}

void printCommands() {
    std::cout << "==== Calibration Tool Commands ====" << std::endl;
    std::cout << "n: Next PCD file" << std::endl;
    std::cout << "b: Previous PCD file" << std::endl;
    std::cout << "p: Add correspondence point pair" << std::endl;
    std::cout << "l: Load correspondence points from matching YAML file" << std::endl;
    std::cout << "c: Run calibration and save results" << std::endl;
    std::cout << "q: Quit application" << std::endl;
    std::cout << "h: Show this help" << std::endl;
}

// Get the base filename without extension
std::string getBaseFilename(const std::string& path) {
    fs::path p(path);
    return p.stem().string();
}

int main(int argc, char** argv) {
    ros::init(argc, argv, "calibration_tool");
    
    if (argc < 4) {
        printUsage();
        return 1;
    }
    
    std::string pcd_dir = argv[1];
    std::string yaml_dir = argv[2];
    std::string tf_json_path = argv[3];
    std::string specific_frame = "";
    
    if (argc >= 5) {
        specific_frame = argv[4];
    }
    
    // Check if directories exist
    if (!fs::exists(pcd_dir) || !fs::is_directory(pcd_dir)) {
        ROS_ERROR("PCD directory does not exist: %s", pcd_dir.c_str());
        return 1;
    }
    
    if (!fs::exists(yaml_dir) || !fs::is_directory(yaml_dir)) {
        ROS_ERROR("YAML directory does not exist: %s", yaml_dir.c_str());
        return 1;
    }
    
    // Check if tf.json exists
    if (!fs::exists(tf_json_path)) {
        ROS_ERROR("tf.json file does not exist: %s", tf_json_path.c_str());
        return 1;
    }
    
    // Collect all PCD files
    std::vector<fs::path> pcd_files;
    for (fs::directory_iterator it(pcd_dir); it != fs::directory_iterator(); ++it) {
        if (fs::is_regular_file(it->status()) && it->path().extension() == ".pcd") {
            if (specific_frame.empty() || getBaseFilename(it->path().string()) == specific_frame) {
                pcd_files.push_back(it->path());
            }
        }
    }
    
    if (pcd_files.empty()) {
        ROS_ERROR("No PCD files found in directory: %s", pcd_dir.c_str());
        return 1;
    }
    
    std::sort(pcd_files.begin(), pcd_files.end());
    
    size_t current_file_idx = 0;
    fs::path current_pcd = pcd_files[current_file_idx];
    std::string current_frame = getBaseFilename(current_pcd.string());
    
    ROS_INFO("Starting with file: %s (frame: %s)", current_pcd.string().c_str(), current_frame.c_str());
    
    CalibrationTool tool(current_frame);
    
    if (!tool.loadPCD(current_pcd.string())) {
        return 1;
    }
    
    // Try to load corresponding YAML file if it exists
    fs::path yaml_path = fs::path(yaml_dir) / (current_frame + ".yaml");
    if (fs::exists(yaml_path)) {
        ROS_INFO("Found matching YAML file: %s", yaml_path.string().c_str());
        tool.loadCorrespondencePointsFromYaml(yaml_path.string(), current_frame);
    }
    
    printCommands();
    
    char cmd;
    bool running = true;
    
    while (running && ros::ok()) {
        std::cout << "Current file: " << current_pcd.filename().string() 
                  << " (" << (current_file_idx + 1) << "/" << pcd_files.size() << ")" << std::endl;
        std::cout << "Enter command (h for help): ";
        cmd = getch();
        std::cout << cmd << std::endl; // Echo the character
        
        switch (cmd) {
            case 'n': {
                // Next file
                current_file_idx = (current_file_idx + 1) % pcd_files.size();
                current_pcd = pcd_files[current_file_idx];
                current_frame = getBaseFilename(current_pcd.string());
                
                ROS_INFO("Loading next file: %s (frame: %s)", 
                         current_pcd.string().c_str(), current_frame.c_str());
                
                // Create new calibration tool with new frame name
                tool = CalibrationTool(current_frame);
                
                if (!tool.loadPCD(current_pcd.string())) {
                    ROS_ERROR("Failed to load PCD file, trying next one");
                    continue;
                }
                
                // Try to load corresponding YAML file if it exists
                fs::path yaml_path = fs::path(yaml_dir) / (current_frame + ".yaml");
                if (fs::exists(yaml_path)) {
                    ROS_INFO("Found matching YAML file: %s", yaml_path.string().c_str());
                    tool.loadCorrespondencePointsFromYaml(yaml_path.string(), current_frame);
                }
                break;
            }
            case 'b': {
                // Previous file
                if (current_file_idx == 0) {
                    current_file_idx = pcd_files.size() - 1;
                } else {
                    current_file_idx--;
                }
                current_pcd = pcd_files[current_file_idx];
                current_frame = getBaseFilename(current_pcd.string());
                
                ROS_INFO("Loading previous file: %s (frame: %s)", 
                         current_pcd.string().c_str(), current_frame.c_str());
                
                // Create new calibration tool with new frame name
                tool = CalibrationTool(current_frame);
                
                if (!tool.loadPCD(current_pcd.string())) {
                    ROS_ERROR("Failed to load PCD file, trying previous one");
                    continue;
                }
                
                // Try to load corresponding YAML file if it exists
                fs::path yaml_path = fs::path(yaml_dir) / (current_frame + ".yaml");
                if (fs::exists(yaml_path)) {
                    ROS_INFO("Found matching YAML file: %s", yaml_path.string().c_str());
                    tool.loadCorrespondencePointsFromYaml(yaml_path.string(), current_frame);
                }
                break;
            }
            case 'p': {
                float lx, ly, lz, wx, wy, wz;
                std::cout << "Enter lidar point (x y z): ";
                std::cin >> lx >> ly >> lz;
                std::cout << "Enter world point (x y z): ";
                std::cin >> wx >> wy >> wz;
                tool.addCorrespondencePoint(pcl::PointXYZ(lx, ly, lz), pcl::PointXYZ(wx, wy, wz));
                // Clear input buffer after using std::cin
                std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
                break;
            }
            case 'l': {
                fs::path yaml_path = fs::path(yaml_dir) / (current_frame + ".yaml");
                if (fs::exists(yaml_path)) {
                    ROS_INFO("Loading from matching YAML file: %s", yaml_path.string().c_str());
                    tool.loadCorrespondencePointsFromYaml(yaml_path.string(), current_frame);
                } else {
                    ROS_ERROR("No matching YAML file found for frame: %s", current_frame.c_str());
                }
                break;
            }
            case 'c': {
                ROS_INFO("Running calibration for frame: %s", current_frame.c_str());
                if (tool.calibrate()) {
                    CalibrationParams params = tool.getCalibrationParams();
                    
                    // Print calibration results
                    ROS_INFO("Calibration successful:");
                    ROS_INFO("Roll: %.6f, Pitch: %.6f, Yaw: %.6f", 
                             params.roll, params.pitch, params.yaw);
                    ROS_INFO("Translation: [%.6f, %.6f, %.6f]", 
                             params.tx, params.ty, params.tz);
                    
                    // Save to tf.json
                    saveCalibrationParams(current_frame, params, tf_json_path);
                    
                    std::cout << "Calibration completed and saved for " << current_frame << std::endl;
                } else {
                    ROS_ERROR("Calibration failed for frame: %s", current_frame.c_str());
                }
                break;
            }
            case 'h': {
                printCommands();
                break;
            }
            case 'q': {
                running = false;
                break;
            }
            default: {
                std::cout << "Unknown command. Press 'h' for help." << std::endl;
                break;
            }
        }
        
        ros::spinOnce();
    }
    
    return 0;
} 
#include "../include/calibration.h"
#include <ros/ros.h>

namespace cotek_laser_detect {

Calibration::Calibration(const std::string& frame_name) : frame_name_(frame_name) {
    // Initialize calibration parameters
    calib_params_.roll = 0.0;
    calib_params_.pitch = 0.0;
    calib_params_.yaw = 0.0;
    calib_params_.tx = 0.0;
    calib_params_.ty = 0.0;
    calib_params_.tz = 0.0;
    
    ROS_INFO("Calibration initialized for frame: %s", frame_name_.c_str());
}

bool Calibration::calibrate(pcl::PointCloud<pcl::PointXYZ>::Ptr cloud, 
                          const std::vector<CorrespondencePoint>& correspondence_points,
                          bool save_debug_files) {
    if (cloud->empty()) {
        ROS_ERROR("Cloud is empty, cannot calibrate");
        return false;
    }
    
    if (correspondence_points.size() < 3) {
        ROS_WARN("Need at least 3 correspondence points for calibration");
        return false;
    }

    // Create a copy of the input cloud to work with
    pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZ>());
    pcl::copyPointCloud(*cloud, *filtered_cloud);
    
    // Perform plane segmentation for ground points
    pcl::ModelCoefficients::Ptr coefficients(new pcl::ModelCoefficients);
    pcl::PointIndices::Ptr inliers(new pcl::PointIndices);
    pcl::SACSegmentation<pcl::PointXYZ> seg;
    
    seg.setOptimizeCoefficients(true);
    seg.setModelType(pcl::SACMODEL_PLANE);
    seg.setMethodType(pcl::SAC_RANSAC);
    seg.setDistanceThreshold(0.05);
    seg.setMaxIterations(5000);
    seg.setProbability(0.99);
    
    seg.setInputCloud(filtered_cloud);
    seg.segment(*inliers, *coefficients);
    
    if (inliers->indices.size() < 5000) {
        ROS_ERROR("Insufficient ground plane points: %zu", inliers->indices.size());
        ROS_INFO("Using region growing algorithm for plane fitting...");
        bool plane_fit_success = fitPlaneWithRegionGrowing(filtered_cloud, inliers, coefficients);
        if (!plane_fit_success) {
            ROS_ERROR("Failed to fit plane using region growing");
            return false;
        }
    }
    
    // Create a copy of plane points for calculations
    pcl::PointCloud<pcl::PointXYZ>::Ptr plane_cloud(new pcl::PointCloud<pcl::PointXYZ>);
    for (const auto& idx : inliers->indices) {
        plane_cloud->push_back(filtered_cloud->points[idx]);
    }
    
    // Save ground plane as PCD file if requested
    if (save_debug_files) {
        std::string ground_pcd_name = package_path_ + "/config/calibrate/ground_plane_" + frame_name_ + ".pcd";
        if (pcl::io::savePCDFileBinary(ground_pcd_name, *plane_cloud) == 0) {
            ROS_INFO("Saved ground plane to %s with %zu points", ground_pcd_name.c_str(), plane_cloud->size());
        } else {
            ROS_ERROR("Failed to save ground plane to %s", ground_pcd_name.c_str());
        }
    }
    
    // Calculate initial transformation parameters from correspondence points
    if (correspondence_points.size() >= 3) {
        Eigen::Matrix3f laser_points, world_points;
        for (int i = 0; i < 3; ++i) {
            const auto& cp = correspondence_points[i];
            laser_points.col(i) << cp.lidar_point.x, cp.lidar_point.y, cp.lidar_point.z;
            world_points.col(i) << cp.world_point.x, cp.world_point.y, cp.world_point.z;
        }

        // Calculate centroids
        Eigen::Vector3f centroid_laser = laser_points.rowwise().mean();
        Eigen::Vector3f centroid_world = world_points.rowwise().mean();

        // Calculate H matrix and SVD decomposition
        Eigen::Matrix3f H = (laser_points.colwise() - centroid_laser) * 
                        (world_points.colwise() - centroid_world).transpose();
        
        Eigen::JacobiSVD<Eigen::Matrix3f> svd(H, Eigen::ComputeFullU | Eigen::ComputeFullV);
        Eigen::Matrix3f R = svd.matrixV() * svd.matrixU().transpose();

        // Handle reflection case
        if (R.determinant() < 0) {
            Eigen::Matrix3f V = svd.matrixV();
            V.col(2) *= -1;
            R = V * svd.matrixU().transpose();
        }

        // Calculate translation
        Eigen::Vector3f T = centroid_world - R * centroid_laser;

        // Convert to ZYX Euler angles
        Eigen::Vector3f euler = R.eulerAngles(2, 1, 0);
        calib_params_.yaw = euler[0];
        calib_params_.pitch = euler[1];
        calib_params_.roll = euler[2];
        calib_params_.tx = T.x();
        calib_params_.ty = T.y();
        calib_params_.tz = T.z();
    }

    // Set optimization parameters
    double params[6] = {
        calib_params_.roll, calib_params_.pitch, calib_params_.yaw,
        calib_params_.tx, calib_params_.ty, calib_params_.tz
    };
    
    // Gauss-Newton configuration
    const int max_iterations = 100;
    const double convergence_threshold = 1e-6;
    const double epsilon = 1e-6;

    // Get point statistics
    const size_t N = inliers->indices.size();         // Ground points count
    const size_t M = correspondence_points.size();    // Correspondence points count
    constexpr double eps = 1e-6;                      // Zero-division protection

    // Automatically calculate weight coefficients
    const double ground_weight = 1.0 / std::sqrt(N + eps);      // Per ground point residual weight
    const double corr_weight = 1.0 / std::sqrt(3*M + eps);      // Per correspondence point residual weight

    for (int iter = 0; iter < max_iterations; ++iter) {
        // Collect all residuals and Jacobian matrices
        std::vector<double> residuals;
        std::vector<Eigen::MatrixXd> jacobians;

        // Process ground point constraints
        for (const auto& idx : inliers->indices) {
            pcl::PointXYZ pt = filtered_cloud->points[idx];
            GroundPlaneResidual residual(pt);
            
            // Calculate residual
            residuals.push_back(residual.computeResidual(params) * ground_weight);
            
            // Calculate numerical Jacobian
            jacobians.push_back(residual.computeNumericalJacobian(params, epsilon).transpose() * ground_weight);
        }

        // Process correspondence point constraints
        for (const auto& cp : correspondence_points) {
            CorrespondenceResidual residual(cp.lidar_point, cp.world_point);
            
            // Calculate residual (3D)
            Eigen::Vector3d r = residual.computeResidual(params);
            residuals.push_back(r[0] * corr_weight);
            residuals.push_back(r[1] * corr_weight);
            residuals.push_back(r[2] * corr_weight);
            
            // Calculate numerical Jacobian (3x6)
            jacobians.push_back(residual.computeNumericalJacobian(params, epsilon) * corr_weight);
        }

        // Build Jacobian matrix and residual vector
        const int num_residuals = residuals.size();
        Eigen::VectorXd R(num_residuals);
        Eigen::MatrixXd J(num_residuals, 6);

        int row_counter = 0;
        for (size_t i = 0; i < jacobians.size(); ++i) {
            const int rows = jacobians[i].rows();
            J.block(row_counter, 0, rows, 6) = jacobians[i];
            R.segment(row_counter, rows) = Eigen::VectorXd::Map(&residuals[row_counter], rows);
            row_counter += rows;
        }

        // Build normal equations: J^T J Δ = -J^T R
        Eigen::MatrixXd Jt = J.transpose();
        Eigen::MatrixXd JtJ = Jt * J;
        Eigen::VectorXd JtR = Jt * R;

        // Solve linear system (using LDLT decomposition)
        Eigen::VectorXd delta = JtJ.ldlt().solve(-JtR);

        // Update parameters
        for (int i = 0; i < 6; ++i) {
            params[i] += delta[i];
        }

        // Check convergence condition
        if (delta.norm() < convergence_threshold) {
            break;
        }
        
        // Calculate and print total error for current iteration
        double total_cost = R.squaredNorm();
        ROS_WARN("Iteration %d: total cost = %.6f, delta norm = %.6f", iter+1, total_cost, delta.norm());
    }
    
    // Store results
    calib_params_.roll = params[0];
    calib_params_.pitch = params[1];
    calib_params_.yaw = params[2];
    calib_params_.tx = params[3];
    calib_params_.ty = params[4];
    calib_params_.tz = params[5];
    
    // Apply transformation to the filtered cloud (for verification)
    if (save_debug_files) {
        pcl::PointCloud<pcl::PointXYZ>::Ptr transformed_cloud(new pcl::PointCloud<pcl::PointXYZ>());
        transformPointCloud(filtered_cloud, transformed_cloud);
        
        // Save transformed cloud as PCD file for verification
        std::string transformed_pcd_name = package_path_ + "/config/calibrate/transformed_cloud_" + frame_name_ + ".pcd";
        if (pcl::io::savePCDFileBinary(transformed_pcd_name, *transformed_cloud) == 0) {
            ROS_INFO("Saved transformed point cloud to %s with %zu points", 
                     transformed_pcd_name.c_str(), transformed_cloud->size());
        } else {
            ROS_ERROR("Failed to save transformed point cloud to %s", transformed_pcd_name.c_str());
        }
    }
    
    ROS_WARN("Calibration Result:");
    ROS_WARN("Roll: %f rad (%f deg)", calib_params_.roll, calib_params_.roll * 180.0 / M_PI);
    ROS_WARN("Pitch: %f rad (%f deg)", calib_params_.pitch, calib_params_.pitch * 180.0 / M_PI);
    ROS_WARN("Yaw: %f rad (%f deg)", calib_params_.yaw, calib_params_.yaw * 180.0 / M_PI);
    ROS_WARN("Translation: [%f, %f, %f]", calib_params_.tx, calib_params_.ty, calib_params_.tz);
    
    return true;
}

void Calibration::transformPointCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& input_cloud,
                                   pcl::PointCloud<pcl::PointXYZ>::Ptr& output_cloud) {
    // Build transformation matrix
    Eigen::Matrix3f R = 
        Eigen::AngleAxisf(calib_params_.yaw, Eigen::Vector3f::UnitZ()) *
        Eigen::AngleAxisf(calib_params_.pitch, Eigen::Vector3f::UnitY()) *
        Eigen::AngleAxisf(calib_params_.roll, Eigen::Vector3f::UnitX())
        .matrix();
    
    Eigen::Affine3f transform = Eigen::Translation3f(calib_params_.tx, calib_params_.ty, calib_params_.tz) * R;
    
    // Apply transformation
    pcl::transformPointCloud(*input_cloud, *output_cloud, transform);
}

bool Calibration::loadCorrespondencePointsFromYaml(const std::string& yaml_file, 
                                                const std::string& frame_name,
                                                std::vector<CorrespondencePoint>& correspondence_points) {
    try {
        YAML::Node config = YAML::LoadFile(yaml_file);
        
        if (!config[frame_name]) {
            ROS_ERROR("Frame '%s' not found in YAML file", frame_name.c_str());
            return false;
        }
        
        YAML::Node points = config[frame_name]["points"];
        if (!points || !points.IsSequence()) {
            ROS_ERROR("No valid points sequence found for frame '%s'", frame_name.c_str());
            return false;
        }
        
        correspondence_points.clear();
        for (const auto& point_pair : points) {
            if (point_pair["lidar_point"] && point_pair["world_point"]) {
                auto lidar_point = point_pair["lidar_point"].as<std::vector<float>>();
                auto world_point = point_pair["world_point"].as<std::vector<float>>();
                
                if (lidar_point.size() == 3 && world_point.size() == 3) {
                    CorrespondencePoint cp;
                    cp.lidar_point = pcl::PointXYZ(lidar_point[0], lidar_point[1], lidar_point[2]);
                    cp.world_point = pcl::PointXYZ(world_point[0], world_point[1], world_point[2]);
                    correspondence_points.push_back(cp);
                }
            }
        }
        
        ROS_INFO("Loaded %zu correspondence points from YAML file", correspondence_points.size());
        return true;
    } catch (const YAML::Exception& e) {
        ROS_ERROR("Error parsing YAML file: %s", e.what());
        return false;
    }
}

CalibrationParams Calibration::getCalibrationParams() const {
    return calib_params_;
}

bool Calibration::fitPlaneWithRegionGrowing(pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud,
                                         pcl::PointIndices::Ptr& inliers, 
                                         pcl::ModelCoefficients::Ptr& coefficients) {
    if (filtered_cloud->empty()) {
        ROS_ERROR("Cloud is empty, cannot fit plane with region growing");
        return false;
    }
    
    ROS_INFO("Starting region growing segmentation, cloud size: %zu points", filtered_cloud->size());
    
    // Calculate point cloud normals
    pcl::NormalEstimation<pcl::PointXYZ, pcl::Normal> ne;
    pcl::PointCloud<pcl::Normal>::Ptr normals(new pcl::PointCloud<pcl::Normal>);
    pcl::search::KdTree<pcl::PointXYZ>::Ptr tree(new pcl::search::KdTree<pcl::PointXYZ>);
    
    ne.setInputCloud(filtered_cloud);
    ne.setSearchMethod(tree);
    ne.setKSearch(30);  // Reduce search range to improve local feature expression
    ROS_INFO("Computing point cloud normals...");
    ne.compute(*normals);
    
    // Region growing segmentation
    pcl::RegionGrowing<pcl::PointXYZ, pcl::Normal> reg;
    reg.setMinClusterSize(500);  // Reduce minimum cluster size to capture planes more easily
    reg.setMaxClusterSize(filtered_cloud->size());
    reg.setSearchMethod(tree);
    reg.setNumberOfNeighbours(20);  // Adjust number of neighbors
    reg.setInputCloud(filtered_cloud);
    reg.setInputNormals(normals);
    reg.setSmoothnessThreshold(5.0 / 180.0 * M_PI);  // Increase to 5 degrees threshold, allow more variation
    reg.setCurvatureThreshold(0.8);  // Lower curvature threshold, more relaxed plane definition
    
    ROS_INFO("Performing region growing segmentation...");
    std::vector<pcl::PointIndices> clusters;
    try {
        reg.extract(clusters);
        ROS_INFO("Found %zu clusters", clusters.size());
    } catch (const std::exception& e) {
        ROS_ERROR("Error during region growing: %s", e.what());
        return false;
    }
    
    if (clusters.empty()) {
        ROS_ERROR("Region growing didn't find any clusters");
        return false;
    }
    
    // Sort clusters by size
    std::sort(clusters.begin(), clusters.end(), 
        [](const pcl::PointIndices& a, const pcl::PointIndices& b) {
            return a.indices.size() > b.indices.size();
        });
    
    // Print the sizes of the top 3 largest clusters
    for (size_t i = 0; i < std::min(size_t(3), clusters.size()); i++) {
        ROS_INFO("Cluster #%zu size: %zu points", i, clusters[i].indices.size());
    }
    
    // Find the largest plane cluster (assumed to be the ground)
    const pcl::PointIndices& largest_cluster = clusters[0];
    
    if (largest_cluster.indices.size() < 500) {
        ROS_ERROR("Largest cluster too small: %zu points", largest_cluster.indices.size());
        return false;
    }
    
    // Extract point cloud from the largest cluster
    pcl::PointCloud<pcl::PointXYZ>::Ptr cluster_cloud(new pcl::PointCloud<pcl::PointXYZ>);
    pcl::ExtractIndices<pcl::PointXYZ> extract;
    extract.setInputCloud(filtered_cloud);
    extract.setIndices(boost::make_shared<pcl::PointIndices>(largest_cluster));
    extract.setNegative(false);
    extract.filter(*cluster_cloud);
    
    ROS_INFO("Extracted largest cluster: %zu points", cluster_cloud->size());
    
    // Use RANSAC to fit plane to the largest cluster
    pcl::SACSegmentation<pcl::PointXYZ> seg;
    seg.setOptimizeCoefficients(true);
    seg.setModelType(pcl::SACMODEL_PLANE);
    seg.setMethodType(pcl::SAC_RANSAC);
    seg.setDistanceThreshold(0.02);  // Use smaller threshold for precise fitting
    seg.setMaxIterations(2000);
    seg.setProbability(0.99);
    
    seg.setInputCloud(cluster_cloud);
    pcl::PointIndices::Ptr final_inliers(new pcl::PointIndices);
    
    ROS_INFO("Fitting plane to largest cluster...");
    try {
        seg.segment(*final_inliers, *coefficients);
    } catch (const std::exception& e) {
        ROS_ERROR("Error during plane fitting: %s", e.what());
        return false;
    }
    
    if (final_inliers->indices.size() < cluster_cloud->size() * 0.7) {
        ROS_WARN("Fitted plane contains fewer points: %.1f%% of cluster points",
            100.0 * final_inliers->indices.size() / cluster_cloud->size());
    } else {
        ROS_INFO("Successfully fitted plane: %zu points (%.1f%% of cluster points)",
            final_inliers->indices.size(),
            100.0 * final_inliers->indices.size() / cluster_cloud->size());
    }
    
    // Convert final inlier indices back to original point cloud indices
    inliers->indices.clear();
    for (const auto& idx : final_inliers->indices) {
        inliers->indices.push_back(largest_cluster.indices[idx]);
    }
    
    ROS_INFO("Plane fitting completed, coefficients: [%.3f, %.3f, %.3f, %.3f]",
        coefficients->values[0], coefficients->values[1],
        coefficients->values[2], coefficients->values[3]);
    
    return !inliers->indices.empty();
}

} // namespace cotek_laser_detect 
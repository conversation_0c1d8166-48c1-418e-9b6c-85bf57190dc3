#include "laser.h"

namespace cotek_laser_detect {

bool Laser::Init() {
  ros::NodeHandle nh;

  for (auto& laser : option_.stock_option) {
    subscribers_.emplace_back(nh.subscribe<sensor_msgs::PointCloud2>(
        laser.first, 10,
        boost::bind(&Laser::MultiLaserCallBack, this, _1)));

    ROS_INFO_STREAM(laser.first << " register callback.");
  }

  return true;
}

void Laser::MultiLaserCallBack(const sensor_msgs::PointCloud2::ConstPtr &msg) {
  std::string frame = msg->header.frame_id;
  VaildPclPointCloud vaild_cloud;
  vaild_cloud.vaild = true;
  pcl::fromROSMsg(*msg, vaild_cloud.cloud);

  std::lock_guard<std::mutex> lock(mutex_);
  pc_[frame] = vaild_cloud;
}

VaildPclPointCloud Laser::GetPointCloud(const std::string& frame) {
  VaildPclPointCloud vaild_cloud;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& pc : pc_) {
      if (pc.first != frame) continue;
      if (pc.second.vaild) {
        vaild_cloud.vaild = true;
        vaild_cloud.cloud = pc.second.cloud;
        pc.second.vaild = false;
      }
    }
  }
  return vaild_cloud;
}

}  // namespace cotek_cam_detect
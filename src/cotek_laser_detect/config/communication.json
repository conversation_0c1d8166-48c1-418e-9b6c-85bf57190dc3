{"communicate": {"server_ip": "*************", "server_port": 9090, "local_ip": "***********", "local_port": 8080, "server_api": "/api/warehouse/warehouseStorage/storage/get", "local_api": "/storage/get", "laser_alive_api": "/api/Radar", "storage_config_api": "/device/service/thirdParty/camera/config", "storage_config_server_ip?": "拉取库位配置的专用服务器IP", "storage_config_server_ip": "*************", "storage_config_server_port?": "拉取库位配置的专用服务器端口", "storage_config_server_port": 80, "storage_config_retry_count?": "库位配置获取失败重试次数", "storage_config_retry_count": 10, "storage_config_retry_interval?": "库位配置获取重试间隔 单位秒", "storage_config_retry_interval": 5, "laser_alive_check_interval?": "激光器存活状态更新间隔 单位秒", "laser_alive_check_interval": 5, "update_duration?": "检测库位时间间隔 单位秒", "update_duration": 1, "accumulate_duration?": "接收一帧点云时间 单位秒", "accumulate_duration": 0.5, "duration?": "定时上报所有库位间隔 单位秒", "duration": 5, "ha?": "高可用配置(可选)", "ha": {"enable?": "是否启用HA模式", "enable": false, "vip_address?": "虚拟IP地址，keepalived配置的VIP", "vip_address": "*************", "interface?": "网络接口名称(可选，如eth0)，留空则检查所有接口", "interface": "", "check_interval?": "HA状态检查间隔，单位秒", "check_interval": 5.0}}}
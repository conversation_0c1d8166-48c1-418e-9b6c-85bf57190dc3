# 配置重新加载HTTP接口说明

## 概述

本系统现在支持通过HTTP接口重新加载库位配置，无需重启进程即可实现配置的动态更新。

## 功能特性

- **动态配置重载**: 通过HTTP API从服务器重新获取最新的库位配置
- **智能重启**: 自动释放所有检测和上报对象，重新构造所有组件
- **单次尝试**: 配置重载只进行一次API尝试，失败时立即返回错误
- **状态监控**: 提供健康检查接口来监控系统状态
- **线程安全**: 使用互斥锁确保重启过程的线程安全

## HTTP接口

### 1. 配置重新加载接口

**端点**: `POST /api/reload-config`  
**端口**: `8080`  
**功能**: 从API重新加载库位配置并重启系统

#### 请求示例
```bash
curl -X POST http://localhost:8080/api/reload-config
```

#### 响应格式
**成功响应**:
```json
{
  "success": true,
  "message": "Config reloaded and system restarted successfully"
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "Failed to reload config from RCS"
}
```

#### 可能的错误情况
- **500**: 系统管理器未初始化
- **500**: 通信配置加载失败
- **500**: API配置获取失败
- **500**: 系统重启失败

### 2. 健康检查接口

**端点**: `GET /api/health`  
**端口**: `8080`  
**功能**: 检查系统运行状态

#### 请求示例
```bash
curl http://localhost:8080/api/health
```

#### 响应格式
**健康状态**:
```json
{
  "status": "healthy",
  "initialized": true
}
```

**不健康状态**:
```json
{
  "status": "unhealthy",
  "initialized": false
}
```

## 工作流程

1. **接收请求**: HTTP服务器接收到 `/api/reload-config` 请求
2. **加载通信配置**: 读取当前的通信配置以获取API信息
3. **单次API尝试**: 调用 `LoadStorageConfigFromAPISingleAttempt` 从API获取最新配置
4. **配置验证**: 验证从API获取的配置格式是否正确
5. **备份现有配置**: 将当前配置文件备份到 `backups/` 目录
6. **保存新配置**: 将新配置保存到本地文件
7. **系统重启**: 释放所有对象并重新初始化
8. **返回结果**: 向客户端返回操作结果

## 系统管理器（SystemManager）

新增的 `SystemManager` 类负责管理所有组件的生命周期：

### 主要功能
- **Initialize()**: 初始化所有系统组件
- **Restart()**: 重启整个系统（清理 + 重新初始化）
- **Shutdown()**: 安全关闭所有组件
- **IsInitialized()**: 检查系统初始化状态

### 管理的组件
- 激光检测器 (LaserDetector)
- 状态报告器 (StatusReporter)
- HA管理器 (HAManager)
- ROS定时器
- ROS NodeHandle

## 配置文件备份

每次从API成功获取新配置时，系统会自动备份现有配置：

- **备份目录**: `{config_dir}/backups/`
- **命名格式**: `{原文件名}.bak_{YYYYMMDD_HHMMSS}`
- **示例**: `storage.json.bak_20231215_143052`

## 使用场景

1. **库位配置更新**: 当仓库布局发生变化时，可以通过此接口更新配置
2. **设备信息变更**: 当激光设备IP地址或区域信息变更时
3. **故障恢复**: 当配置文件损坏时，可以从服务器重新获取
4. **远程维护**: 运维人员可以远程更新配置而无需重启服务

## 注意事项

1. **单次尝试**: 配置重载只尝试一次API调用，失败时不会重试
2. **阻塞操作**: 重启过程中系统会短暂停止检测和上报功能
3. **配置兼容性**: 确保API返回的配置格式与本地配置文件格式兼容
4. **网络依赖**: 需要确保能够访问配置API服务器
5. **权限要求**: 确保程序有权限创建备份目录和写入配置文件

## 日志说明

重新加载过程中的关键日志信息：

```
[INFO] Received config reload request via HTTP
[INFO] Attempting to load storage config from API (single attempt)
[INFO] Restarting system...
[INFO] All objects cleaned up
[INFO] Initializing system...
[INFO] System restarted successfully
[INFO] Config reload and system restart completed successfully
```

## 监控建议

建议监控以下指标：
- HTTP接口响应时间
- 配置重载成功率
- 系统重启耗时
- 配置备份文件数量

通过这些接口，你可以实现配置的动态更新，提高系统的运维效率和灵活性。 